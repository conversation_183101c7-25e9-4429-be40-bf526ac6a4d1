import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'dart:io';

import 'connectivity_detail_page.dart'; // Changed to connectivity_detail_page
import 'login_page.dart';

class ConnectivityPage extends StatefulWidget { // Renamed to ConnectivityPage
  final bool isDarkMode;
  final VoidCallback toggleTheme;
  final String collegeNameForTable;
  final List<Map<String, dynamic>>? preloadedConnectivity; // Changed to connectivity
  final bool isFromDetailPage;

  const ConnectivityPage({ // Renamed to ConnectivityPage
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
    required this.collegeNameForTable,
    this.preloadedConnectivity, // Changed to connectivity
    this.isFromDetailPage = false,
  }) : super(key: key);

  @override
  _ConnectivityPageState createState() => _ConnectivityPageState(); // Changed state name
}

class _ConnectivityPageState extends State<ConnectivityPage> // Changed state name
    with AutomaticKeepAliveClientMixin {
  final ScrollController _scrollController = ScrollController();
  final PageStorageKey _listKey = const PageStorageKey('connectivity_list'); // Changed key
  bool _isDisposed = false;
  List<Map<String, dynamic>> _connectivity = []; // Changed to connectivity
  bool _isLoading = false;
  late final RealtimeChannel _realtimeChannel;
  int _page = 0;
  final int _pageSize = 20;
  bool _hasMore = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _setupRealtime();
    _scrollController.addListener(_onScroll);
  }

  Future<void> _loadInitialData() async {
    if (widget.preloadedConnectivity != null && widget.preloadedConnectivity!.isNotEmpty) {
      setState(() {
        _connectivity = List<Map<String, dynamic>>.from(widget.preloadedConnectivity!);
        _connectivity.forEach((point) {
          point['_isImageLoading'] = false;
        });
        _connectivity.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _hasMore = widget.preloadedConnectivity!.length == _pageSize;
      });
      _loadConnectivityFromSupabase(initialLoad: false); // Changed method name
    } else {
      _loadConnectivityFromSupabase(initialLoad: true); // Changed method name
    }
  }

  Future<void> _loadConnectivityFromSupabase({bool initialLoad = true}) async { // Changed method name
    if (_isLoading || (!_hasMore && !initialLoad)) return;

    setState(() {
      _isLoading = true;
    });
    
    // Changed table name to connectivity
    final connectivityTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_connectivity';

    try {
      final response = await Supabase.instance.client
          .from(connectivityTableName) // Changed to connectivity table
          .select('*')
          .order('fullname', ascending: true)
          .range(_page * _pageSize, (_page + 1) * _pageSize - 1);

      final updatedConnectivity =
          await _updateConnectivityImageUrls(List<Map<String, dynamic>>.from(response)); // Changed

      setState(() {
        if (initialLoad) {
          _connectivity = updatedConnectivity;
        } else {
          _connectivity.addAll(updatedConnectivity);
        }
        _connectivity.forEach((point) {
          point['_isImageLoading'] = false;
        });
        _connectivity.sort((a, b) =>
            (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
        _isLoading = false;
        _hasMore = response.length == _pageSize;
      });
    } catch (error) {
      if (!_isDisposed) {
        String errorMsg;
        final errorStr = error.toString().toLowerCase();
        if (errorStr.contains('socketexception') || errorStr.contains('offline')) {
          errorMsg = "Offline. Please check your internet connection.";
          _showOfflineSnackbar();
        } else if (errorStr.contains('relation') && errorStr.contains('does not exist')) {
          errorMsg = "Connectivity information hasn't been added yet.";
          _showErrorSnackbar(errorMsg);
        } else {
          errorMsg = "Error fetching connectivity points: $error";
          _showErrorSnackbar(errorMsg);
        }
        setState(() {
          _isLoading = false;
          _hasMore = false;
        });
      }
    }
  }

  Future<List<Map<String, dynamic>>> _updateConnectivityImageUrls( // Changed method name
      List<Map<String, dynamic>> connectivity) async {
    List<Future<void>> futures = [];
    for (final point in connectivity) {
      if (point['image_url'] == null ||
          point['image_url'] == 'assets/placeholder_image.png') {
        futures.add(_fetchImageUrl(point));
      }
    }
    await Future.wait(futures);
    return connectivity;
  }

  void _setupRealtime() {
    // Changed to connectivity table
    final connectivityTableName =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}_connectivity';
    
    _realtimeChannel = Supabase.instance.client
        .channel('connectivity') // Changed channel name
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: connectivityTableName, // Changed to connectivity table
          callback: (payload) async {
            if (payload.eventType == PostgresChangeEvent.insert) {
              final newPointId = payload.newRecord['id'];
              final newPointResponse = await Supabase.instance.client
                  .from(connectivityTableName)
                  .select('')
                  .eq('id', newPointId)
                  .single();
              if (mounted) {
                Map<String, dynamic> newPoint = Map.from(newPointResponse);
                final updatedPoint = await _updateConnectivityImageUrls([newPoint]);
                setState(() {
                  _connectivity = [..._connectivity, updatedPoint.first];
                  updatedPoint.first['_isImageLoading'] = false;
                  _connectivity.sort((a, b) =>
                      (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
                });
              }
            } else if (payload.eventType == PostgresChangeEvent.update) {
              final updatedPointId = payload.newRecord['id'];
              final updatedPointResponse = await Supabase.instance.client
                  .from(connectivityTableName)
                  .select('')
                  .eq('id', updatedPointId)
                  .single();
              if (mounted) {
                final updatedPoint = Map<String, dynamic>.from(updatedPointResponse);
                setState(() {
                  _connectivity = _connectivity.map((point) {
                    return point['id'] == updatedPoint['id'] ? updatedPoint : point;
                  }).toList();
                  _connectivity.sort((a, b) =>
                      (a['fullname'] ?? '').toLowerCase().compareTo((b['fullname'] ?? '').toLowerCase()));
                });
              }
            } else if (payload.eventType == PostgresChangeEvent.delete) {
              final deletedPointId = payload.oldRecord['id'];
              setState(() {
                _connectivity.removeWhere((point) => point['id'] == deletedPointId);
              });
            }
          },
        )
        .subscribe();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _realtimeChannel.unsubscribe();
    super.dispose();
  }

  void _onScroll() {
    if (!_isDisposed &&
        _scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200) {
      _loadMoreConnectivity(); // Changed method name
    }
  }

  Future<void> _loadMoreConnectivity() async { // Changed method name
    if (!_isLoading && _hasMore) {
      _page++;
      await _loadConnectivityFromSupabase(initialLoad: false); // Changed method name
    }
  }

  @override
  bool get wantKeepAlive => true;

  void _navigateToDetail(BuildContext context, Map<String, dynamic> point) {
    if (!_isDisposed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ConnectivityDetailPage( // Changed to ConnectivityDetailPage
            point: point,
            isDarkMode: widget.isDarkMode,
            toggleTheme: widget.toggleTheme,
            collegeNameForBucket: widget.collegeNameForTable,
          ),
        ),
      );
    }
  }

  void _showOfflineSnackbar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text("Offline. Please check your internet connection."),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  void _showErrorSnackbar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.redAccent,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final currentIsDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Connectivity'), // Changed title
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: theme.colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => _loadConnectivityFromSupabase(initialLoad: true), // Changed method
              child: _connectivity.isEmpty
                  ? LayoutBuilder(
                      builder: (BuildContext context, BoxConstraints constraints) {
                        return SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: SizedBox(
                            height: constraints.maxHeight,
                            child: const Center(
                              child: Text('No connectivity points available.'),
                            ),
                          ),
                        );
                      },
                    )
                  : ListView.builder(
                      key: _listKey,
                      controller: _scrollController,
                      shrinkWrap: true,
                      physics: const AlwaysScrollableScrollPhysics(),
                      padding: const EdgeInsets.all(16),
                      itemCount: _connectivity.length + (_hasMore ? 1 : 0),
                      itemBuilder: (context, index) {
                        if (index < _connectivity.length) {
                          final point = _connectivity[index];
                          return VisibilityDetector(
                            key: Key('connectivity_${point['id']}'), // Changed key
                            onVisibilityChanged: (VisibilityInfo info) {
                              if (info.visibleFraction > 0.1 &&
                                  (point['image_url'] == null ||
                                      point['image_url'] == 'assets/placeholder_image.png') &&
                                  !point['_isImageLoading']) {
                                _fetchImageUrl(point);
                              }
                            },
                            child: _buildConnectivityCard(point, theme, currentIsDarkMode), // Changed
                          );
                        } else if (_hasMore) {
                          return const Center(
                              child: Padding(
                                  padding: EdgeInsets.all(16),
                                  child: CircularProgressIndicator()));
                        } else {
                          return Container();
                        }
                      },
                    ),
            ),
      bottomNavigationBar: Container(
        color: theme.colorScheme.surface,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  icon: Icon(
                    Icons.home_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    currentIsDarkMode
                        ? Icons.light_mode_outlined
                        : Icons.dark_mode_outlined,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: widget.toggleTheme,
                ),
                const SizedBox(width: 24),
                IconButton(
                  icon: Icon(
                    Icons.person_outline,
                    color: theme.colorScheme.onSurface,
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPage(
                          isDarkMode: currentIsDarkMode,
                          toggleTheme: widget.toggleTheme,
                        ),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _fetchImageUrl(Map<String, dynamic> point) async {
    if (point['_isImageLoading'] == true) return;
    if (point['image_url'] != null &&
        point['image_url'] != 'assets/placeholder_image.png') {
      return;
    }

    setState(() {
      point['_isImageLoading'] = true;
    });

    final fullname = point['fullname'] as String? ?? '';
    final imageNameWebp = '$fullname.webp';
    String imageUrl = '';
    // Changed bucket to connectivity
    final collegeConnectivityBucket =
        '${widget.collegeNameForTable.toLowerCase().replaceAll(' ', '')}/connectivity';

    try {
      final file = await Supabase.instance.client.storage.from(collegeConnectivityBucket).download(imageNameWebp);
      if (file.isNotEmpty) {
        imageUrl = Supabase.instance.client.storage.from(collegeConnectivityBucket).getPublicUrl(imageNameWebp);
      }
    } catch (e) {
      // Error handled silently
    }

    if (mounted) {
      setState(() {
        point['image_url'] = imageUrl.isNotEmpty ? imageUrl : 'assets/placeholder_image.png';
        point['_isImageLoading'] = false;
      });
    } else {
      point['_isImageLoading'] = false;
    }
  }

  Widget _buildConnectivityCard( // Changed method name and parameters
      Map<String, dynamic> point,
      ThemeData theme,
      bool isDarkMode,
      ) {
    final String fullname = point['fullname'] ?? 'Unknown';
    final String about = point['about'] ?? '';
    final String imageUrl = point['image_url'] ?? '';

    return Card(
      color: theme.colorScheme.surface,
      surfaceTintColor: Colors.transparent,
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => _navigateToDetail(context, point),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              imageUrl.isNotEmpty && imageUrl != 'assets/placeholder_image.png'
                  ? ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: imageUrl,
                        width: 48,
                        height: 48,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => CircleAvatar(
                          radius: 24,
                          backgroundColor: isDarkMode
                              ? Colors.white.withOpacity(0.1)
                              : Colors.black.withOpacity(0.1),
                          child: Icon(
                            Icons.wifi, // Changed to wifi icon
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                        errorWidget: (context, url, error) => CircleAvatar(
                          radius: 24,
                          backgroundColor: isDarkMode
                              ? Colors.white.withOpacity(0.1)
                              : Colors.black.withOpacity(0.1),
                          child: Icon(
                            Icons.wifi, // Changed to wifi icon
                            color: isDarkMode ? Colors.white : Colors.black,
                          ),
                        ),
                      ),
                    )
                  : CircleAvatar(
                      radius: 24,
                      backgroundColor: isDarkMode
                          ? Colors.white.withOpacity(0.1)
                          : Colors.black.withOpacity(0.1),
                      child: Icon(
                        Icons.wifi, // Changed to wifi icon
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fullname,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (about.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          about,
                          style: TextStyle(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: 13,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }
}