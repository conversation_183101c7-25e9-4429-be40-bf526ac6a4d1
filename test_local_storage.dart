// Test file to demonstrate the local storage functionality
// This file can be run to test the local storage service

import 'dart:io';
import 'lib/services/local_storage_service.dart';

void main() async {
  print('Testing Local Storage Service...');
  
  final storageService = LocalStorageService();
  
  try {
    // Test 1: Create a course
    print('\n1. Creating a test course...');
    final course = await storageService.createCourse('Test Course');
    print('✅ Course created: ${course.name} (ID: ${course.id})');
    
    // Test 2: Save content
    print('\n2. Saving test content...');
    final content = await storageService.saveContent(
      title: 'Test Content',
      content: 'This is a test content for the local storage system.',
      contentType: 'notes',
      courseId: course.id,
    );
    print('✅ Content saved: ${content.title} (ID: ${content.id})');
    
    // Test 3: Load all courses
    print('\n3. Loading all courses...');
    final courses = await storageService.getCourses();
    print('✅ Found ${courses.length} courses:');
    for (final c in courses) {
      print('   - ${c.name} (${c.id})');
    }
    
    // Test 4: Load all content
    print('\n4. Loading all saved content...');
    final contents = await storageService.getSavedContents();
    print('✅ Found ${contents.length} content items:');
    for (final c in contents) {
      print('   - ${c.title} (Type: ${c.contentType}, Course: ${c.courseId})');
    }
    
    // Test 5: Update content
    print('\n5. Updating content...');
    final updatedContent = content.copyWith(title: 'Updated Test Content');
    await storageService.updateSavedContent(updatedContent);
    print('✅ Content updated successfully');
    
    // Test 6: Test drag-and-drop functionality (sort order)
    print('\n6. Testing sort order functionality...');
    final allContents = await storageService.getSavedContents();
    if (allContents.length > 1) {
      // Reverse the order
      final reversedContents = allContents.reversed.toList();
      await storageService.updateContentSortOrder(reversedContents);
      print('✅ Sort order updated successfully');
    } else {
      print('ℹ️  Need more content items to test sort order');
    }
    
    print('\n🎉 All tests passed! Local storage service is working correctly.');
    
  } catch (e) {
    print('❌ Test failed: $e');
    exit(1);
  }
}
