import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

// Data models for local storage
class SavedContent {
  final String id;
  final String title;
  final String content;
  final String contentType;
  final DateTime createdAt;
  final String? courseId;
  final int sortOrder;

  SavedContent({
    required this.id,
    required this.title,
    required this.content,
    required this.contentType,
    required this.createdAt,
    this.courseId,
    this.sortOrder = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'contentType': contentType,
      'createdAt': createdAt.toIso8601String(),
      'courseId': courseId,
      'sortOrder': sortOrder,
    };
  }

  factory SavedContent.fromJson(Map<String, dynamic> json) {
    return SavedContent(
      id: json['id'],
      title: json['title'],
      content: json['content'],
      contentType: json['contentType'],
      createdAt: DateTime.parse(json['createdAt']),
      courseId: json['courseId'],
      sortOrder: json['sortOrder'] ?? 0,
    );
  }

  SavedContent copyWith({
    String? id,
    String? title,
    String? content,
    String? contentType,
    DateTime? createdAt,
    String? courseId,
    int? sortOrder,
  }) {
    return SavedContent(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      contentType: contentType ?? this.contentType,
      createdAt: createdAt ?? this.createdAt,
      courseId: courseId ?? this.courseId,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}

class Course {
  final String id;
  final String name;
  final DateTime createdAt;
  final int sortOrder;

  Course({
    required this.id,
    required this.name,
    required this.createdAt,
    this.sortOrder = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'createdAt': createdAt.toIso8601String(),
      'sortOrder': sortOrder,
    };
  }

  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'],
      name: json['name'],
      createdAt: DateTime.parse(json['createdAt']),
      sortOrder: json['sortOrder'] ?? 0,
    );
  }

  Course copyWith({
    String? id,
    String? name,
    DateTime? createdAt,
    int? sortOrder,
  }) {
    return Course(
      id: id ?? this.id,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}

// Local Storage Service
class LocalStorageService {
  static const String _savedContentDir = 'saved_content';
  static const String _metadataFile = 'metadata.json';
  static const String _coursesFile = 'courses.json';

  // Get the saved content directory
  Future<Directory> _getSavedContentDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final savedContentDir = Directory(path.join(appDir.path, _savedContentDir));
    
    if (!await savedContentDir.exists()) {
      await savedContentDir.create(recursive: true);
    }
    
    return savedContentDir;
  }

  // Load all saved content
  Future<List<SavedContent>> getSavedContents() async {
    try {
      final dir = await _getSavedContentDirectory();
      final metadataFile = File(path.join(dir.path, _metadataFile));
      
      if (!await metadataFile.exists()) {
        return [];
      }
      
      final metadataContent = await metadataFile.readAsString();
      final List<dynamic> metadataList = json.decode(metadataContent);
      
      List<SavedContent> contents = [];
      
      for (final metadata in metadataList) {
        try {
          final contentFile = File(path.join(dir.path, metadata['fileName']));
          if (await contentFile.exists()) {
            final content = await contentFile.readAsString();
            contents.add(SavedContent(
              id: metadata['id'],
              title: metadata['title'],
              content: content,
              contentType: metadata['contentType'],
              createdAt: DateTime.parse(metadata['createdAt']),
              courseId: metadata['courseId'],
              sortOrder: metadata['sortOrder'] ?? 0,
            ));
          }
        } catch (e) {
          print('Error loading content ${metadata['id']}: $e');
        }
      }
      
      // Sort by sortOrder
      contents.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
      
      return contents;
    } catch (e) {
      print('Error loading saved contents: $e');
      return [];
    }
  }

  // Save content
  Future<SavedContent> saveContent({
    required String title,
    required String content,
    required String contentType,
    String? courseId,
  }) async {
    try {
      final dir = await _getSavedContentDirectory();
      final contentId = DateTime.now().millisecondsSinceEpoch.toString();
      final fileName = '${contentId}_${title.replaceAll(' ', '_').replaceAll(':', '-')}.txt';
      
      // Save the actual content
      final contentFile = File(path.join(dir.path, fileName));
      await contentFile.writeAsString(content);
      
      // Load existing metadata
      final metadataFile = File(path.join(dir.path, _metadataFile));
      List<Map<String, dynamic>> metadataList = [];
      
      if (await metadataFile.exists()) {
        try {
          final metadataContent = await metadataFile.readAsString();
          final List<dynamic> existingData = json.decode(metadataContent);
          metadataList = existingData.cast<Map<String, dynamic>>();
        } catch (e) {
          metadataList = [];
        }
      }
      
      // Add new metadata entry
      final newContent = SavedContent(
        id: contentId,
        title: title,
        content: content,
        contentType: contentType,
        createdAt: DateTime.now(),
        courseId: courseId,
        sortOrder: metadataList.length,
      );
      
      metadataList.add({
        'id': contentId,
        'title': title,
        'contentType': contentType,
        'fileName': fileName,
        'courseId': courseId,
        'createdAt': newContent.createdAt.toIso8601String(),
        'sortOrder': metadataList.length,
      });
      
      // Save updated metadata
      await metadataFile.writeAsString(json.encode(metadataList));
      
      return newContent;
    } catch (e) {
      throw Exception('Failed to save content: $e');
    }
  }

  // Update saved content
  Future<void> updateSavedContent(SavedContent content) async {
    try {
      final dir = await _getSavedContentDirectory();
      final metadataFile = File(path.join(dir.path, _metadataFile));
      
      if (!await metadataFile.exists()) {
        throw Exception('Metadata file not found');
      }
      
      final metadataContent = await metadataFile.readAsString();
      final List<dynamic> metadataList = json.decode(metadataContent);
      
      // Find and update the metadata entry
      for (int i = 0; i < metadataList.length; i++) {
        if (metadataList[i]['id'] == content.id) {
          metadataList[i]['title'] = content.title;
          metadataList[i]['courseId'] = content.courseId;
          metadataList[i]['sortOrder'] = content.sortOrder;
          break;
        }
      }
      
      // Save updated metadata
      await metadataFile.writeAsString(json.encode(metadataList));
      
      // Update the content file if title changed
      final oldFileName = metadataList.firstWhere((m) => m['id'] == content.id)['fileName'];
      final newFileName = '${content.id}_${content.title.replaceAll(' ', '_').replaceAll(':', '-')}.txt';
      
      if (oldFileName != newFileName) {
        final oldFile = File(path.join(dir.path, oldFileName));
        final newFile = File(path.join(dir.path, newFileName));
        
        if (await oldFile.exists()) {
          await oldFile.copy(newFile.path);
          await oldFile.delete();
          
          // Update filename in metadata
          for (int i = 0; i < metadataList.length; i++) {
            if (metadataList[i]['id'] == content.id) {
              metadataList[i]['fileName'] = newFileName;
              break;
            }
          }
          await metadataFile.writeAsString(json.encode(metadataList));
        }
      }
    } catch (e) {
      throw Exception('Failed to update content: $e');
    }
  }

  // Delete saved content
  Future<void> deleteSavedContent(String contentId) async {
    try {
      final dir = await _getSavedContentDirectory();
      final metadataFile = File(path.join(dir.path, _metadataFile));

      if (!await metadataFile.exists()) {
        return;
      }

      final metadataContent = await metadataFile.readAsString();
      final List<dynamic> metadataList = json.decode(metadataContent);

      // Find and remove the metadata entry
      String? fileName;
      metadataList.removeWhere((metadata) {
        if (metadata['id'] == contentId) {
          fileName = metadata['fileName'];
          return true;
        }
        return false;
      });

      // Delete the content file
      if (fileName != null) {
        final contentFile = File(path.join(dir.path, fileName!));
        if (await contentFile.exists()) {
          await contentFile.delete();
        }
      }

      // Save updated metadata
      await metadataFile.writeAsString(json.encode(metadataList));
    } catch (e) {
      throw Exception('Failed to delete content: $e');
    }
  }

  // Update content sort order (for drag-and-drop)
  Future<void> updateContentSortOrder(List<SavedContent> contents) async {
    try {
      final dir = await _getSavedContentDirectory();
      final metadataFile = File(path.join(dir.path, _metadataFile));

      if (!await metadataFile.exists()) {
        return;
      }

      final metadataContent = await metadataFile.readAsString();
      final List<dynamic> metadataList = json.decode(metadataContent);

      // Update sort order for each content
      for (int i = 0; i < contents.length; i++) {
        final content = contents[i];
        for (int j = 0; j < metadataList.length; j++) {
          if (metadataList[j]['id'] == content.id) {
            metadataList[j]['sortOrder'] = i;
            break;
          }
        }
      }

      // Save updated metadata
      await metadataFile.writeAsString(json.encode(metadataList));
    } catch (e) {
      throw Exception('Failed to update sort order: $e');
    }
  }

  // Load all courses
  Future<List<Course>> getCourses() async {
    try {
      final dir = await _getSavedContentDirectory();
      final coursesFile = File(path.join(dir.path, _coursesFile));

      if (!await coursesFile.exists()) {
        return [];
      }

      final coursesContent = await coursesFile.readAsString();
      final List<dynamic> coursesList = json.decode(coursesContent);

      List<Course> courses = coursesList.map((courseData) => Course.fromJson(courseData)).toList();

      // Sort by sortOrder
      courses.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));

      return courses;
    } catch (e) {
      print('Error loading courses: $e');
      return [];
    }
  }

  // Create a new course
  Future<Course> createCourse(String name) async {
    try {
      final dir = await _getSavedContentDirectory();
      final coursesFile = File(path.join(dir.path, _coursesFile));

      List<Course> courses = [];

      if (await coursesFile.exists()) {
        try {
          final coursesContent = await coursesFile.readAsString();
          final List<dynamic> coursesList = json.decode(coursesContent);
          courses = coursesList.map((courseData) => Course.fromJson(courseData)).toList();
        } catch (e) {
          courses = [];
        }
      }

      final newCourse = Course(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: name,
        createdAt: DateTime.now(),
        sortOrder: courses.length,
      );

      courses.add(newCourse);

      // Save updated courses
      final coursesJson = courses.map((course) => course.toJson()).toList();
      await coursesFile.writeAsString(json.encode(coursesJson));

      return newCourse;
    } catch (e) {
      throw Exception('Failed to create course: $e');
    }
  }

  // Update course
  Future<void> updateCourse(Course course) async {
    try {
      final dir = await _getSavedContentDirectory();
      final coursesFile = File(path.join(dir.path, _coursesFile));

      if (!await coursesFile.exists()) {
        throw Exception('Courses file not found');
      }

      final coursesContent = await coursesFile.readAsString();
      final List<dynamic> coursesList = json.decode(coursesContent);

      // Find and update the course
      for (int i = 0; i < coursesList.length; i++) {
        if (coursesList[i]['id'] == course.id) {
          coursesList[i] = course.toJson();
          break;
        }
      }

      // Save updated courses
      await coursesFile.writeAsString(json.encode(coursesList));
    } catch (e) {
      throw Exception('Failed to update course: $e');
    }
  }

  // Delete course
  Future<void> deleteCourse(String courseId) async {
    try {
      final dir = await _getSavedContentDirectory();
      final coursesFile = File(path.join(dir.path, _coursesFile));

      if (!await coursesFile.exists()) {
        return;
      }

      final coursesContent = await coursesFile.readAsString();
      final List<dynamic> coursesList = json.decode(coursesContent);

      // Remove the course
      coursesList.removeWhere((course) => course['id'] == courseId);

      // Save updated courses
      await coursesFile.writeAsString(json.encode(coursesList));

      // Remove course association from all content
      final metadataFile = File(path.join(dir.path, _metadataFile));
      if (await metadataFile.exists()) {
        final metadataContent = await metadataFile.readAsString();
        final List<dynamic> metadataList = json.decode(metadataContent);

        for (int i = 0; i < metadataList.length; i++) {
          if (metadataList[i]['courseId'] == courseId) {
            metadataList[i]['courseId'] = null;
          }
        }

        await metadataFile.writeAsString(json.encode(metadataList));
      }
    } catch (e) {
      throw Exception('Failed to delete course: $e');
    }
  }

  // Update course sort order (for drag-and-drop)
  Future<void> updateCourseSortOrder(List<Course> courses) async {
    try {
      final dir = await _getSavedContentDirectory();
      final coursesFile = File(path.join(dir.path, _coursesFile));

      // Update sort order for each course
      for (int i = 0; i < courses.length; i++) {
        courses[i] = courses[i].copyWith(sortOrder: i);
      }

      // Save updated courses
      final coursesJson = courses.map((course) => course.toJson()).toList();
      await coursesFile.writeAsString(json.encode(coursesJson));
    } catch (e) {
      throw Exception('Failed to update course sort order: $e');
    }
  }
}
