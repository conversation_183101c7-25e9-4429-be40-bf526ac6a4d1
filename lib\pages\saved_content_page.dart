import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/local_storage_service.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import '../widgets/latex_helpers.dart' as latex_helpers;

class SavedContentPage extends StatefulWidget {
  final bool isDarkMode;
  final VoidCallback toggleTheme;

  const SavedContentPage({
    Key? key,
    required this.isDarkMode,
    required this.toggleTheme,
  }) : super(key: key);

  @override
  State<SavedContentPage> createState() => _SavedContentPageState();
}

class _SavedContentPageState extends State<SavedContentPage> with SingleTickerProviderStateMixin {
  final LocalStorageService _storageService = LocalStorageService();
  List<SavedContent> _savedContents = [];
  List<Course> _courses = [];
  bool _isLoading = true;
  String? _selectedCourseId;
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final contents = await _storageService.getSavedContents();
      final courses = await _storageService.getCourses();
      
      setState(() {
        _savedContents = contents;
        _courses = courses;
        _isLoading = false;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading data: $e')),
      );
      setState(() => _isLoading = false);
    }
  }
  
  Future<void> _createCourse() async {
    final TextEditingController controller = TextEditingController();
    
    final courseName = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Create New Course', 
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: 'Enter course name',
            hintStyle: TextStyle(color: widget.isDarkMode ? Colors.white70 : Colors.black54),
          ),
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, controller.text),
            child: Text('Create', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
        ],
      ),
    );
    
    if (courseName != null && courseName.isNotEmpty) {
      try {
        final course = await _storageService.createCourse(courseName);
        setState(() {
          _courses.add(course);
          _selectedCourseId = course.id;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Course "$courseName" created')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error creating course: $e')),
        );
      }
    }
  }
  
  Future<void> _renameCourse(Course course) async {
    final TextEditingController controller = TextEditingController(text: course.name);
    
    final newName = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Rename Course', 
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: 'Enter new course name',
            hintStyle: TextStyle(color: widget.isDarkMode ? Colors.white70 : Colors.black54),
          ),
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, controller.text),
            child: Text('Rename', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
        ],
      ),
    );
    
    if (newName != null && newName.isNotEmpty) {
      try {
        final updatedCourse = course.copyWith(name: newName);

        await _storageService.updateCourse(updatedCourse);

        setState(() {
          final index = _courses.indexWhere((c) => c.id == course.id);
          if (index != -1) {
            _courses[index] = updatedCourse;
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Course renamed to "$newName"')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error renaming course: $e')),
        );
      }
    }
  }
  
  Future<void> _deleteCourse(Course course) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Course', 
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
        content: Text(
          'Are you sure you want to delete "${course.name}"? Content items will be preserved but removed from this course.',
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('Delete', 
              style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
    
    if (confirm == true) {
      try {
        await _storageService.deleteCourse(course.id);
        
        setState(() {
          _courses.removeWhere((c) => c.id == course.id);
          if (_selectedCourseId == course.id) {
            _selectedCourseId = null;
          }
          
          // Update saved contents to remove course association
          for (int i = 0; i < _savedContents.length; i++) {
            if (_savedContents[i].courseId == course.id) {
              _savedContents[i] = _savedContents[i].copyWith(courseId: null);
            }
          }
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Course "${course.name}" deleted')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting course: $e')),
        );
      }
    }
  }
  
  Future<void> _renameContent(SavedContent content) async {
    final TextEditingController controller = TextEditingController(text: content.title);
    
    final newTitle = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Rename Content', 
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            hintText: 'Enter new title',
            hintStyle: TextStyle(color: widget.isDarkMode ? Colors.white70 : Colors.black54),
          ),
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, controller.text),
            child: Text('Rename', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
        ],
      ),
    );
    
    if (newTitle != null && newTitle.isNotEmpty) {
      try {
        final updatedContent = content.copyWith(title: newTitle);

        await _storageService.updateSavedContent(updatedContent);

        setState(() {
          final index = _savedContents.indexWhere((c) => c.id == content.id);
          if (index != -1) {
            _savedContents[index] = updatedContent;
          }
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Content renamed to "$newTitle"')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error renaming content: $e')),
        );
      }
    }
  }
  
  Future<void> _deleteContent(SavedContent content) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Content', 
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
        content: Text(
          'Are you sure you want to delete "${content.title}"? This action cannot be undone.',
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('Delete', 
              style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
    
    if (confirm == true) {
      try {
        await _storageService.deleteSavedContent(content.id);
        
        setState(() {
          _savedContents.removeWhere((c) => c.id == content.id);
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Content "${content.title}" deleted')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting content: $e')),
        );
      }
    }
  }
  
  Future<void> _assignToCourse(SavedContent content) async {
    final selectedCourseId = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Assign to Course', 
          style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView(
            shrinkWrap: true,
            children: [
              ListTile(
                title: Text('None (Remove from course)', 
                  style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                onTap: () => Navigator.pop(context, null),
              ),
              ..._courses.map((course) => ListTile(
                title: Text(course.name, 
                  style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
                selected: content.courseId == course.id,
                selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                onTap: () => Navigator.pop(context, course.id),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel', 
              style: TextStyle(color: widget.isDarkMode ? Colors.white : Colors.black)),
          ),
        ],
      ),
    );
    
    if (selectedCourseId != content.courseId) {
      try {
        final updatedContent = content.copyWith(courseId: selectedCourseId);

        await _storageService.updateSavedContent(updatedContent);

        setState(() {
          final index = _savedContents.indexWhere((c) => c.id == content.id);
          if (index != -1) {
            _savedContents[index] = updatedContent;
          }
        });

        final courseName = selectedCourseId == null
          ? 'no course'
          : '"${_courses.firstWhere((c) => c.id == selectedCourseId).name}"';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Content assigned to $courseName')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error assigning content to course: $e')),
        );
      }
    }
  }
  
  Future<void> _viewContent(SavedContent content) async {
    final generalTextColor = widget.isDarkMode ? Colors.white : Colors.black;
    
    await showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              AppBar(
                title: Text(content.title),
                backgroundColor: Theme.of(context).colorScheme.surface,
                foregroundColor: generalTextColor,
                actions: [
                  IconButton(
                    icon: Icon(Icons.edit, color: generalTextColor),
                    onPressed: () {
                      Navigator.pop(context);
                      _renameContent(content);
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.folder, color: generalTextColor),
                    onPressed: () {
                      Navigator.pop(context);
                      _assignToCourse(content);
                    },
                  ),
                  IconButton(
                    icon: Icon(Icons.delete, color: generalTextColor),
                    onPressed: () {
                      Navigator.pop(context);
                      _deleteContent(content);
                    },
                  ),
                ],
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: _buildContentView(content, generalTextColor),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildContentView(SavedContent content, Color textColor) {
    // Check if content contains LaTeX
    final latexRegExp = RegExp(r'(\$\$.*?\$\$|\$.*?\$|\\\[.*?\\\]|\\\(.*?\\\))', dotAll: true);
    if (latexRegExp.hasMatch(content.content)) {
      return latex_helpers.buildLatexContent(
        content.content,
        widget.isDarkMode,
        16.0,
      );
    }
    
    // Regular markdown content
    return MarkdownBody(
      data: content.content,
      styleSheet: MarkdownStyleSheet(
        p: GoogleFonts.notoSans(color: textColor, fontSize: 16),
        h1: GoogleFonts.notoSans(color: textColor, fontWeight: FontWeight.bold, fontSize: 24),
        h2: GoogleFonts.notoSans(color: textColor, fontWeight: FontWeight.bold, fontSize: 20),
        h3: GoogleFonts.notoSans(color: textColor, fontWeight: FontWeight.bold, fontSize: 18),
        strong: GoogleFonts.notoSans(color: textColor, fontWeight: FontWeight.w700),
        em: GoogleFonts.notoSans(color: textColor, fontStyle: FontStyle.italic),
        code: GoogleFonts.sourceCodePro(fontSize: 14, backgroundColor: Theme.of(context).cardColor, color: textColor),
      ),
    );
  }
  
  List<SavedContent> _getFilteredContents() {
    if (_selectedCourseId == null) {
      return _savedContents;
    } else {
      return _savedContents.where((content) => content.courseId == _selectedCourseId).toList();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final generalTextColor = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonBackground = widget.isDarkMode ? Colors.white : Colors.black;
    final buttonTextColor = widget.isDarkMode ? Colors.black : Colors.white;
    
    return Scaffold(
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        title: Text(
          'Saved Content',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(
              widget.isDarkMode ? Icons.light_mode : Icons.dark_mode,
              color: theme.colorScheme.onSurface,
            ),
            onPressed: widget.toggleTheme,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: generalTextColor,
          tabs: const [
            Tab(text: 'All Content'),
            Tab(text: 'Courses'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                // All Content Tab
                Column(
                  children: [
                    if (_courses.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: DropdownButton<String?>(
                          isExpanded: true,
                          hint: Text('Filter by Course', style: TextStyle(color: generalTextColor)),
                          value: _selectedCourseId,
                          items: [
                            DropdownMenuItem<String?>(
                              value: null,
                              child: Text('All Content', style: TextStyle(color: generalTextColor)),
                            ),
                            ..._courses.map((course) => DropdownMenuItem<String?>(
                              value: course.id,
                              child: Text(course.name, style: TextStyle(color: generalTextColor)),
                            )),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedCourseId = value;
                            });
                          },
                          dropdownColor: theme.colorScheme.surface,
                        ),
                      ),
                    Expanded(
                      child: _getFilteredContents().isEmpty
                          ? Center(
                              child: Text(
                                'No saved content found',
                                style: TextStyle(color: generalTextColor),
                              ),
                            )
                          : ReorderableListView.builder(
                              itemCount: _getFilteredContents().length,
                              onReorder: (oldIndex, newIndex) async {
                                if (oldIndex < newIndex) {
                                  newIndex -= 1;
                                }

                                final filteredContents = _getFilteredContents();
                                final content = filteredContents.removeAt(oldIndex);
                                filteredContents.insert(newIndex, content);

                                // Update the main list with new order
                                if (_selectedCourseId == null) {
                                  // Reordering all content
                                  _savedContents = filteredContents;
                                } else {
                                  // Reordering content within a specific course
                                  final otherContents = _savedContents.where((c) => c.courseId != _selectedCourseId).toList();
                                  _savedContents = [...otherContents, ...filteredContents];
                                }

                                // Update sort orders in storage
                                try {
                                  await _storageService.updateContentSortOrder(_savedContents);
                                } catch (e) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('Error updating content order: $e')),
                                  );
                                }

                                setState(() {});
                              },
                              itemBuilder: (context, index) {
                                final content = _getFilteredContents()[index];
                                final course = content.courseId != null
                                    ? _courses.firstWhere(
                                        (c) => c.id == content.courseId,
                                        orElse: () => Course(
                                          id: '',
                                          name: 'Unknown Course',
                                          createdAt: DateTime.now(),
                                          sortOrder: 0,
                                        ),
                                      )
                                    : null;

                                return Card(
                                  key: ValueKey(content.id),
                                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  child: ListTile(
                                    title: Text(
                                      content.title,
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: generalTextColor,
                                      ),
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Type: ${content.contentType}',
                                          style: TextStyle(color: generalTextColor.withOpacity(0.7)),
                                        ),
                                        if (course != null)
                                          Text(
                                            'Course: ${course.name}',
                                            style: TextStyle(color: generalTextColor.withOpacity(0.7)),
                                          ),
                                        Text(
                                          'Created: ${content.createdAt.toString().substring(0, 16)}',
                                          style: TextStyle(color: generalTextColor.withOpacity(0.7)),
                                        ),
                                      ],
                                    ),
                                    leading: Icon(Icons.drag_handle, color: generalTextColor),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: Icon(Icons.edit, color: generalTextColor),
                                          onPressed: () => _renameContent(content),
                                        ),
                                        IconButton(
                                          icon: Icon(Icons.folder, color: generalTextColor),
                                          onPressed: () => _assignToCourse(content),
                                        ),
                                        IconButton(
                                          icon: Icon(Icons.delete, color: generalTextColor),
                                          onPressed: () => _deleteContent(content),
                                        ),
                                      ],
                                    ),
                                    onTap: () => _viewContent(content),
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
                
                // Courses Tab
                Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: ElevatedButton.icon(
                        icon: Icon(Icons.add, color: buttonTextColor),
                        label: Text('Create New Course', style: TextStyle(color: buttonTextColor)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: buttonBackground,
                          minimumSize: const Size(double.infinity, 50),
                        ),
                        onPressed: _createCourse,
                      ),
                    ),
                    Expanded(
                      child: _courses.isEmpty
                          ? Center(
                              child: Text(
                                'No courses found',
                                style: TextStyle(color: generalTextColor),
                              ),
                            )
                          : ReorderableListView.builder(
                              itemCount: _courses.length,
                              onReorder: (oldIndex, newIndex) async {
                                if (oldIndex < newIndex) {
                                  newIndex -= 1;
                                }
                                
                                final course = _courses.removeAt(oldIndex);
                                _courses.insert(newIndex, course);
                                
                                // Update sort orders
                                try {
                                  await _storageService.updateCourseSortOrder(_courses);
                                } catch (e) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text('Error updating course order: $e')),
                                  );
                                }
                                
                                setState(() {});
                              },
                              itemBuilder: (context, index) {
                                final course = _courses[index];
                                final contentCount = _savedContents
                                    .where((content) => content.courseId == course.id)
                                    .length;
                                
                                return Card(
                                  key: ValueKey(course.id),
                                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  child: ListTile(
                                    title: Text(
                                      course.name,
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: generalTextColor,
                                      ),
                                    ),
                                    subtitle: Text(
                                      '$contentCount items',
                                      style: TextStyle(color: generalTextColor.withOpacity(0.7)),
                                    ),
                                    leading: Icon(Icons.drag_handle, color: generalTextColor),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        IconButton(
                                          icon: Icon(Icons.edit, color: generalTextColor),
                                          onPressed: () => _renameCourse(course),
                                        ),
                                        IconButton(
                                          icon: Icon(Icons.delete, color: generalTextColor),
                                          onPressed: () => _deleteCourse(course),
                                        ),
                                      ],
                                    ),
                                    onTap: () {
                                      setState(() {
                                        _selectedCourseId = course.id;
                                        _tabController.animateTo(0); // Switch to content tab
                                      });
                                    },
                                  ),
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadData,
        backgroundColor: buttonBackground,
        child: Icon(Icons.refresh, color: buttonTextColor),
      ),
    );
  }
}
