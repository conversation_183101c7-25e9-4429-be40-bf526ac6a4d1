---------------------------
-- Table: thecollegeofwooster_helpdesks
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_helpdesks;
CREATE TABLE thecollegeofwooster_helpdesks (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_helpdesks (fullname, building, room, hours, payment, phone, email, fax, whatsapp, about, latitude, longitude) VALUES
-- IT Help Desk (located in the Computer Center)
('IT Help Desk', 'Computer Center', 'Room 101', 'Mon-Fri 8am-8pm', 'Free', '************', '<EMAIL>', '************', '************', 'Technical support for campus computing and network issues.', 41.9940, -81.5550),
-- Student Services Help Desk (in the Student Center)
('Student Services Help Desk', 'Student Center', 'Main Desk', 'Mon-Fri 9am-5pm', 'Free', '************', '<EMAIL>', '************', '************', 'Assistance with registration, scheduling, and campus resources.', 41.9955, -81.5565),
-- Financial Aid Help Desk (located in the Administration Building)
('Financial Aid Help Desk', 'Administration Building', 'Suite 12', 'Mon-Fri 8am-4pm', 'Free', '************', '<EMAIL>', '************', '************', 'Guidance on financial aid options, scholarships, and tuition payment plans.', 41.9960, -81.5570),
-- Library Information Desk (located in the William A. Henry Library)
('Library Information Desk', 'William A. Henry Library', 'Lobby', 'Mon-Fri 8am-10pm', 'Free', '************', '<EMAIL>', '************', '************', 'Assistance with research, circulation, and library services.', 41.9970, -81.5580);

---------------------------
-- Table: thecollegeofwooster_accessibility
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_accessibility;
CREATE TABLE thecollegeofwooster_accessibility (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_accessibility (fullname, building, room, hours, phone, whatsapp, about, latitude, longitude) VALUES
-- Accessible Services Office (in the Academic Center)
('Accessible Services Office', 'Academic Center', 'Room 230', 'Mon-Fri 8am-5pm', '************', '************', 'Coordinates support for students requiring accessibility accommodations.', 41.9980, -81.5590),
-- Disability Support Center (in the Center for Student Affairs)
('Disability Support Center', 'Center for Student Affairs', 'Office 105', 'Mon-Fri 8am-5pm', '************', '************', 'Provides resources and counseling for students with disabilities.', 41.9985, -81.5600),
-- Health & Accessibility Unit (at the Wellness Center)
('Health & Accessibility Unit', 'Wellness Center', 'Room B12', 'Mon-Fri 9am-5pm', '************', '************', 'Integrates health services with accessibility support.', 41.9990, -81.5610),
-- Campus Access Liaison (at the Community Building)
('Campus Access Liaison', 'Community Building', 'Room 305', 'Mon-Fri 8am-6pm', '************', '************', 'Advises on campus navigation and facility access.', 42.0000, -81.5620);

---------------------------
-- Table: thecollegeofwooster_faq
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_faq;
CREATE TABLE thecollegeofwooster_faq (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    department TEXT,
    school TEXT,
    topic TEXT,
    studentquestion TEXT,
    facultyorstaffquestion TEXT,
    parentquestion TEXT,
    admissionsquestion TEXT,
    orientationquestion TEXT,
    symposiumquestion TEXT,
    graduationquestion TEXT,
    alumniquestion TEXT
);

INSERT INTO thecollegeofwooster_faq (fullname, about, department, school, topic, studentquestion, facultyorstaffquestion, parentquestion, admissionsquestion, orientationquestion, symposiumquestion, graduationquestion, alumniquestion) VALUES
-- Admissions FAQ
('Admissions FAQ', 'Details about applying and joining The College of Wooster.', 'Admissions', 'Undergraduate', 'Application Process', 
 'How do I apply for admission?', 
 'What should faculty mention when recommending applicants?', 
 'What are the admission requirements for my child?', 
 'When is the application deadline?', 
 'What orientation events are scheduled for new students?', 
 'Will there be an admissions symposium?', 
 'What are the criteria for graduation?', 
 'How can alumni remain involved?'),
-- Financial Aid FAQ
('Financial Aid FAQ', 'Information on tuition, aid, and scholarships.', 'Financial Aid', 'Undergraduate', 'Tuition & Scholarships', 
 'How do I apply for financial aid?', 
 'How can staff support students in need?', 
 'What financial aid options exist for parents?', 
 'Is there an early admission financial aid process?', 
 'How is orientation addressing financial planning?', 
 'Are there symposium sessions on funding education?', 
 'What are the scholarship renewal requirements?', 
 'How do alumni access alumni loans?'),
-- Academic Advising FAQ
('Academic Advising FAQ', 'Guidance for course selection and academic planning.', 'Academic Affairs', 'Undergraduate', 'Course Registration', 
 'Who can help me plan my schedule?', 
 'What resources are available for faculty advising?', 
 'Where can parents find academic progress updates?', 
 'Do admissions advisors help with course selection?', 
 'What orientation sessions cover academic policies?', 
 'Is there a symposium on academic success?', 
 'What academic requirements must be completed for graduation?', 
 'Can alumni provide mentorship in course planning?'),
-- Student Life FAQ
('Student Life FAQ', 'Answers to common questions about campus life and extracurricular activities.', 'Student Affairs', 'Undergraduate', 'Campus Involvement', 
 'What clubs and organizations are available?', 
 'How can faculty get involved in student activities?', 
 'How is campus life structured to support students?', 
 'Do admissions events include student life presentations?', 
 'What orientation activities focus on student life?', 
 'Will there be a symposium on student engagement?', 
 'What are the prerequisites for graduation events?', 
 'How can alumni participate in campus life?');

---------------------------
-- Table: thecollegeofwooster_links
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_links;
CREATE TABLE thecollegeofwooster_links (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    link TEXT,
    admissionslink TEXT,
    orientationlink TEXT,
    symposiumlink TEXT,
    alumnilink TEXT,
    department TEXT,
    img_link TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_links (fullname, link, admissionslink, orientationlink, symposiumlink, alumnilink, department, img_link, about) VALUES
-- Admissions & Financial Aid Portal
('Admissions & Financial Aid', 'https://www.wooster.edu/admissions', 'https://www.wooster.edu/admissions/apply', 'https://www.wooster.edu/admissions/orientation', 'https://www.wooster.edu/admissions/symposium', 'https://www.wooster.edu/alumni', 'Admissions', 'https://www.wooster.edu/assets/img/admissions.jpg', 'Portal for all things related to admission and financial aid.'),
-- Academic Departments
('Academic Departments', 'https://www.wooster.edu/academics', 'https://www.wooster.edu/academics/admissions', 'https://www.wooster.edu/academics/orientation', 'https://www.wooster.edu/academics/symposium', 'https://www.wooster.edu/alumni/departments', 'Academics', 'https://www.wooster.edu/assets/img/academics.jpg', 'Information on academic programs and departmental resources.'),
-- Student Life and Campus Engagement
('Student Life & Campus Engagement', 'https://www.wooster.edu/studentlife', 'https://www.wooster.edu/studentlife/admissions', 'https://www.wooster.edu/studentlife/orientation', 'https://www.wooster.edu/studentlife/symposium', 'https://www.wooster.edu/alumni/studentlife', 'Student Affairs', 'https://www.wooster.edu/assets/img/studentlife.jpg', 'Resources and events that enrich campus life.'),
-- Campus Facilities & Infrastructure
('Campus Facilities', 'https://www.wooster.edu/campus', 'https://www.wooster.edu/campus/admissions', 'https://www.wooster.edu/campus/orientation', 'https://www.wooster.edu/campus/symposium', 'https://www.wooster.edu/alumni/campus', 'Facilities', 'https://www.wooster.edu/assets/img/facilities.jpg', 'Updates on campus construction, renovations, and facility management.');

---------------------------
-- Table: thecollegeofwooster_construction
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_construction;
CREATE TABLE thecollegeofwooster_construction (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    location TEXT,
    startdate DATE,
    enddate DATE,
    phone TEXT,
    whatsapp TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_construction (fullname, location, startdate, enddate, phone, whatsapp, latitude, longitude, about) VALUES
-- Library Renovation at the William A. Henry Library
('Library Renovation', 'William A. Henry Library', '2025-07-01', '2025-12-15', '************', '************', 41.9970, -81.5585, 'Renovation of study spaces and archival facilities to improve modern research support.'),
-- Science Center Expansion
('Science Center Expansion', 'Science Building', '2025-06-15', '2025-11-30', '************', '************', 41.9980, -81.5595, 'Expanding laboratories and lecture halls to accommodate growing STEM programs.'),
-- Student Center Update
('Student Center Update', 'Student Center', '2025-05-20', '2025-10-15', '************', '************', 41.9990, -81.5605, 'Upgrading common areas and meeting rooms to foster student engagement and activities.'),
-- Athletic Complex Upgrade
('Athletic Complex Upgrade', 'Athletic Center', '2025-08-01', '2025-12-01', '************', '************', 42.0000, -81.5615, 'Modernizing gymnasiums, fields, and exercise facilities to support athletics.');

---------------------------
-- Table: thecollegeofwooster_printing
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_printing;
CREATE TABLE thecollegeofwooster_printing (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_printing (fullname, building, room, hours, payment, phone, whatsapp, about, latitude, longitude) VALUES
-- Campus Printing Services at the University Print Center
('Campus Printing Services', 'University Print Center', 'Room 101', 'Mon-Fri 8am-8pm', 'Cash/Card', '************', '************', 'Provides fast, high-quality printing and copying services for students and staff.', 42.0010, -81.5620),
-- Digital Print Lab (located in the Media Center)
('Digital Print Lab', 'Media Center', 'Lab 201', 'Mon-Fri 9am-7pm', 'Card Only', '************', '************', 'Offers digital printing, scanning, and binding services.', 42.0020, -81.5630),
-- Student Copy Services (located in the Library)
('Student Copy Services', 'William A. Henry Library', 'Copy Room', 'Mon-Fri 8am-10pm', 'Free for enrolled students', '************', '************', 'Supports academic and research copy needs on campus.', 42.0030, -81.5640),
-- Print & Bind (in the Administration Building)
('Print & Bind', 'Administration Building', 'Office 302', 'Mon-Fri 8am-5pm', 'Cash/Card', '************', '************', 'Specializes in document printing, binding, and finishing services.', 42.0040, -81.5650);

---------------------------
-- Table: thecollegeofwooster_daycares
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_daycares;
CREATE TABLE thecollegeofwooster_daycares (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    boxnumber TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_daycares (fullname, building, room, hours, phone, boxnumber, email, fax, whatsapp, about, latitude, longitude) VALUES
-- Wooster Child Care Center
('Wooster Child Care Center', 'Family Life Center', 'Room A1', 'Mon-Fri 7am-6pm', '************', 'BOX-101', '<EMAIL>', '************', '************', 'Offers quality care for children of students and staff with age-appropriate programs.', 42.0050, -81.5660),
-- Campus Daycare
('Campus Daycare', 'Community Center', 'Room B2', 'Mon-Fri 7:30am-5:30pm', '************', 'BOX-102', '<EMAIL>', '************', '************', 'Provides a safe and engaging environment for young children during the day.', 42.0060, -81.5670),
-- Early Learning Center
('Early Learning Center', 'Academic Center', 'Room C3', 'Mon-Fri 8am-5pm', '************', 'BOX-103', '<EMAIL>', '************', '************', 'Focuses on early childhood development and educational enrichment programs.', 42.0070, -81.5680),
-- Infant Care Program
('Infant Care Program', 'Student Services Building', 'Room D4', 'Mon-Fri 7am-4pm', '330-672-4600', 'BOX-104', '<EMAIL>', '330-672-4601', '330-672-4602', 'Specializes in infant care with customized programs for babies and toddlers.', 42.0080, -81.5690);

---------------------------
-- Table: thecollegeofwooster_sustainability
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_sustainability;
CREATE TABLE thecollegeofwooster_sustainability (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_sustainability (fullname, about) VALUES
-- Office of Sustainability
('Office of Sustainability', 'Leads campus initiatives on energy efficiency, waste reduction, and environmental stewardship.'),
-- Green Campus Initiative
('Green Campus Initiative', 'Promotes recycling, water conservation, and sustainable practices across campus.'),
-- Environmental Research Center
('Environmental Research Center', 'Conducts research and community outreach on climate change and sustainable development.'),
-- Sustainable Transportation Office
('Sustainable Transportation Office', 'Coordinates bike programs, carpooling, and alternative transit to reduce the campus carbon footprint.');



----------------------------------

---------------------------
-- Table: thecollegeofwooster_notices
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_notices;
CREATE TABLE thecollegeofwooster_notices (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    day INTEGER,
    month INTEGER,
    year INTEGER,
    link TEXT
);

INSERT INTO thecollegeofwooster_notices (fullname, about, day, month, year, link) VALUES
-- Spring Semester Announcement
('Spring Semester Begins', 'The College of Wooster welcomes students for the spring term with updated schedules and campus protocols.', 15, 1, 2025, 'https://www.wooster.edu/notices/spring2025'),
-- Health & Safety Update
('COVID-19 Health Update', 'New guidelines and resources regarding campus health and safety measures have been released.', 10, 2, 2025, 'https://www.wooster.edu/notices/health-safety'),
-- Campus Maintenance Notice
('Library Renovation Notice', 'Renovation work at the William A. Henry Library will cause temporary closures of select study areas.', 22, 3, 2025, 'https://www.wooster.edu/notices/library-renovation'),
-- Upcoming Guest Lecture
('Guest Lecture: Future of STEM', 'Join us for a special lecture featuring renowned experts discussing emerging trends in STEM fields.', 5, 4, 2025, 'https://www.wooster.edu/notices/stem-lecture');

---------------------------
-- Table: thecollegeofwooster_socialmediafeeds
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_socialmediafeeds;
CREATE TABLE thecollegeofwooster_socialmediafeeds (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    platform TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_socialmediafeeds (fullname, platform, link) VALUES
-- Wooster Official Facebook Page
('Wooster Facebook', 'Facebook', 'https://www.facebook.com/CollegeofWooster'),
-- Wooster Official Twitter Account
('Wooster Twitter', 'Twitter', 'https://www.twitter.com/CollegeofWooster'),
-- Wooster Official Instagram Account
('Wooster Instagram', 'Instagram', 'https://www.instagram.com/CollegeofWooster'),
-- Wooster Official LinkedIn Page
('Wooster LinkedIn', 'LinkedIn', 'https://www.linkedin.com/school/the-college-of-wooster');

---------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_admissionsprocess;
CREATE TABLE thecollegeofwooster_admissionsprocess (
    id SERIAL PRIMARY KEY,
    stepnumber INT,
    stepname TEXT,
    description TEXT,
    link TEXT,
    department TEXT,
    targetaudience TEXT,
    estimatedtime TEXT,
    requiredmaterials TEXT
);

INSERT INTO thecollegeofwooster_admissionsprocess (
    stepnumber, stepname, description, link, department, targetaudience, estimatedtime, requiredmaterials
) VALUES
(1, 'Explore Wooster', 
 'Start by learning about The College of Wooster’s academic programs, campus life, and community through the website and virtual tours.', 
 'https://wooster.edu/admissions/explore', 
 'Admissions', 
 'prospective students', 
 '1 week', 
 'Internet access'),

(2, 'Schedule a Campus Visit', 
 'Visit the campus either in person or virtually to experience what it’s like to be a Wooster student.', 
 'https://wooster.edu/admissions/visit', 
 'Admissions', 
 'prospective students, parents', 
 '1-2 days', 
 'None'),

(3, 'Complete the Application', 
 'Submit your application through the Common App or Coalition App. Include all required materials.', 
 'https://wooster.edu/admissions/apply', 
 'Admissions', 
 'prospective students', 
 '2-3 weeks', 
 'Application form, transcripts, test scores (optional), personal essay'),

(4, 'Submit Recommendation Letters', 
 'Provide academic recommendations from your high school teachers or counselors.', 
 'https://wooster.edu/admissions/requirements', 
 'Admissions', 
 'prospective students', 
 '1 week', 
 '1–2 letters of recommendation'),

(5, 'Interview (Optional but Encouraged)', 
 'Participate in an admissions interview to help us learn more about you and your interests.', 
 'https://wooster.edu/admissions/interview', 
 'Admissions', 
 'prospective students', 
 '1 day', 
 'None'),

(6, 'Apply for Financial Aid', 
 'Complete the FAFSA and Wooster’s financial aid forms to be considered for scholarships and aid.', 
 'https://wooster.edu/financial-aid', 
 'Admissions', 
 'prospective students, parents', 
 '2 weeks', 
 'FAFSA form, tax documents'),

(7, 'Track Your Application Status', 
 'Check your admissions portal regularly for updates, missing documents, or decisions.', 
 'https://wooster.edu/status', 
 'Admissions', 
 'prospective students', 
 'Ongoing', 
 'Login credentials'),

(8, 'Receive Admission Decision', 
 'You will be notified of the decision by the published dates depending on your application round.', 
 'https://wooster.edu/admissions/decisions', 
 'Admissions', 
 'prospective students, parents', 
 'Varies', 
 'None'),

(9, 'Confirm Your Enrollment', 
 'Once accepted, confirm your spot by submitting the enrollment deposit and completing required forms.', 
 'https://wooster.edu/enroll', 
 'Admissions', 
 'prospective students, parents', 
 '1 week', 
 'Enrollment deposit, final transcripts');


DROP TABLE IF EXISTS thecollegeofwooster_registrationprocess;
CREATE TABLE thecollegeofwooster_registrationprocess (
    id SERIAL PRIMARY KEY,
    stepnumber INT,
    stepname TEXT,
    description TEXT,
    link TEXT,
    department TEXT,
    targetaudience TEXT,
    term TEXT,
    deadline DATE
);

INSERT INTO thecollegeofwooster_registrationprocess (
    stepnumber, stepname, description, link, department, targetaudience, term, deadline
) VALUES
(1, 'Review Course Catalog',
 'Browse available courses and requirements for your major/minor to plan your semester schedule.',
 'https://wooster.edu/academics/catalog',
 'Academic Advising',
 'current students',
 'Fall 2025',
 '2025-03-25'),

(2, 'Meet with Academic Advisor',
 'Discuss your course plan and degree progress with your assigned academic advisor.',
 'https://wooster.edu/academics/advising',
 'Academic Advising',
 'current students',
 'Fall 2025',
 '2025-04-01'),

(3, 'Clear Holds',
 'Ensure there are no holds on your account that might prevent registration (e.g., financial, medical).',
 'https://wooster.edu/students/holds',
 'Registrar',
 'current students',
 'Fall 2025',
 '2025-04-05'),

(4, 'Register for Courses',
 'Log into the registration portal and enroll in your selected classes.',
 'https://wooster.edu/registration',
 'Registrar',
 'current students',
 'Fall 2025',
 '2025-04-10'),

(5, 'Confirm Registration',
 'Double-check your schedule, resolve any conflicts, and ensure your course load meets credit requirements.',
 'https://wooster.edu/registration/status',
 'Registrar',
 'current students',
 'Fall 2025',
 '2025-04-12'),

(6, 'Request Overrides or Waitlists (if needed)',
 'Submit override forms or join waitlists for full courses.',
 'https://wooster.edu/registration/override',
 'Registrar',
 'current students',
 'Fall 2025',
 '2025-04-15'),

(7, 'Finalize Schedule',
 'Make any final schedule changes before the deadline to avoid late fees or penalties.',
 'https://wooster.edu/registration/finalize',
 'Registrar',
 'current students',
 'Fall 2025',
 '2025-04-20');



---------------------------
-- Table: thecollegeofwooster_selection
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_selection;
CREATE TABLE thecollegeofwooster_selection (
    id SERIAL PRIMARY KEY,
    firstname TEXT,
    lastname TEXT,
    gender TEXT,
    year INTEGER,
    major TEXT,
    nationality TEXT,
    yearofentry INTEGER
);

INSERT INTO thecollegeofwooster_selection (firstname, lastname, gender, year, major, nationality, yearofentry) VALUES
('Alice', 'Cooper', 'Female', 2, 'Biology', 'USA', 2023),
('Michael', 'Anderson', 'Male', 3, 'History', 'USA', 2022),
('Emma', 'Thompson', 'Female', 1, 'Economics', 'Canada', 2024),
('David', 'Martinez', 'Male', 4, 'Physics', 'USA', 2021);

---------------------------
-- Table: thecollegeofwooster_costsorrates
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_costsorrates;
CREATE TABLE thecollegeofwooster_costsorrates (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    amount NUMERIC,
    about TEXT,
    categoryormajor TEXT
);

INSERT INTO thecollegeofwooster_costsorrates (fullname, amount, about, categoryormajor) VALUES
('Tuition Fee', 45000, 'Annual tuition fee for full-time undergraduate students.', 'Undergraduate'),
('Room & Board', 12000, 'Estimated cost for housing and meal plan for an academic year.', 'Undergraduate'),
('Graduate Tuition', 30000, 'Annual tuition fee for graduate programs.', 'Graduate'),
('Lab Fees', 1500, 'Fees for laboratory and specialized equipment usage in STEM programs.', 'STEM');

---------------------------
-- Table: thecollegeofwooster_scholarships
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_scholarships;
CREATE TABLE thecollegeofwooster_scholarships (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    major TEXT
);

INSERT INTO thecollegeofwooster_scholarships (fullname, about, major) VALUES
('Academic Excellence Scholarship', 'Awarded to students with outstanding academic records.', 'All Majors'),
('STEM Innovators Scholarship', 'Supports students excelling in science, technology, engineering, and math.', 'STEM'),
('Arts Leadership Award', 'Recognizes leadership and creative excellence in the arts.', 'Arts'),
('Global Citizenship Scholarship', 'For students with significant international experience and community service.', 'International Studies');

---------------------------
-- Table: thecollegeofwooster_payments
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_payments;
CREATE TABLE thecollegeofwooster_payments (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    link TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_payments (fullname, link, about) VALUES
('Online Payment Portal', 'https://www.wooster.edu/payments', 'Make secure online tuition and fees payments.'),
('Payment Schedule', 'https://www.wooster.edu/payments/schedule', 'View the academic calendar with payment deadlines.'),
('Payment Assistance', 'https://www.wooster.edu/payments/assistance', 'Find information on installment plans and financial support options.'),
('Billing Inquiries', 'https://www.wooster.edu/payments/billing', 'Contact the billing department for invoice details and questions.');

---------------------------
-- Table: thecollegeofwooster_orientations
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_orientations;
CREATE TABLE thecollegeofwooster_orientations (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT
);

INSERT INTO thecollegeofwooster_orientations (fullname, about, phone, email, fax) VALUES
('Fall Orientation 2025', 'Orientation program for new undergraduate students starting Fall 2025.', '************', '<EMAIL>', '************'),
('Spring Orientation 2025', 'Orientation session for transfer and returning students for Spring 2025 term.', '************', '<EMAIL>', '************'),
('International Student Orientation', 'Dedicated orientation for new international students.', '************', '<EMAIL>', '************'),
('Graduate Orientation', 'Orientation for newly admitted graduate students.', '************', '<EMAIL>', '************');

---------------------------
-- Table: thecollegeofwooster_symposiums
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_symposiums;
CREATE TABLE thecollegeofwooster_symposiums (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT
);

INSERT INTO thecollegeofwooster_symposiums (fullname, about, phone, email, fax) VALUES
('Research Symposium 2025', 'Annual symposium showcasing undergraduate and graduate research projects.', '************', '<EMAIL>', '************'),
('Arts & Humanities Symposium', 'Forum for presentations and discussions in the arts and humanities.', '************', '<EMAIL>', '************'),
('Science & Technology Symposium', 'Exhibiting breakthroughs and projects in STEM fields.', '************', '<EMAIL>', '************'),
('Social Sciences Symposium', 'Panel discussions and presentations on contemporary social issues.', '************', '<EMAIL>', '************');

---------------------------
-- Table: thecollegeofwooster_graduation
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_graduation;
CREATE TABLE thecollegeofwooster_graduation (
    id SERIAL PRIMARY KEY,
    venue TEXT,
    alternatelocation TEXT,
    speaker TEXT,
    starttime TEXT,
    endtime TEXT,
    day INTEGER,
    month INTEGER,
    year INTEGER,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_graduation (venue, alternatelocation, speaker, starttime, endtime, day, month, year, about, latitude, longitude) VALUES
('Wooster Convocation Hall', 'Main Campus Lawn', 'Dr. Jane Goodall', '10:00 AM', '12:00 PM', 20, 5, 2025, 'Commencement ceremony for undergraduate graduates.', 41.9975, -81.5585),
('William A. Henry Library Auditorium', 'Student Center Atrium', 'Dean Robert Smith', '2:00 PM', '4:00 PM', 20, 5, 2025, 'Ceremony for graduate students featuring keynote speeches.', 41.9980, -81.5590),
('Academic Center Auditorium', 'Outdoor Pavilion', 'Alumna Lisa Ray', '9:00 AM', '11:00 AM', 15, 5, 2025, 'Graduation for summer session students.', 41.9960, -81.5575),
('Student Union Complex', 'University Quad', 'President Michael Thompson', '1:00 PM', '3:00 PM', 25, 5, 2025, 'Commencement event celebrating academic achievements.', 41.9950, -81.5560);

-------------------------
---------------------------
-- Table: thecollegeofwooster_people
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_people;
CREATE TABLE thecollegeofwooster_people (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    title TEXT,
    department TEXT,
    school TEXT,
    school2 TEXT,
    school3 TEXT,
    building TEXT,
    room TEXT,
    officehours TEXT,
    phone TEXT,
    ext TEXT,
    email TEXT,
    fax TEXT,
    hours TEXT,
    facultymember BOOLEAN,
    staffmember BOOLEAN,
    orientationcontact BOOLEAN,
    graduationcontact BOOLEAN,
    about TEXT,
    services TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_people (fullname, title, department, school, school2, school3, building, room, officehours, phone, ext, email, fax, hours, facultymember, staffmember, orientationcontact, graduationcontact, about, services, latitude, longitude) VALUES
-- Row 1: Faculty Member – Professor of Biology
('Dr. Emily Johnson', 
 'Professor of Biology', 
 'Biology', 
 'College of Sciences', 
 'Department of Life Sciences', 
 'Graduate Studies', 
 'Science Building', 
 'Room 210', 
 'Tue/Thu 1pm-3pm', 
 '************', 
 '123', 
 '<EMAIL>', 
 '************', 
 'Mon-Fri 9am-5pm', 
 true, 
 false, 
 false, 
 true, 
 'Dr. Johnson specializes in molecular biology and genetics, leading innovative research and mentoring students.', 
 'Research, Lectures, Academic Advising', 
 41.9950, 
 -81.5550),

-- Row 2: Staff – Academic Advisor
('Mr. John Lee', 
 'Academic Advisor', 
 'Advising & Counseling', 
 'Student Affairs', 
 'Undergraduate Services', 
 'Continuing Education', 
 'Student Center', 
 'Office 102', 
 'Mon/Wed 10am-12pm', 
 '************', 
 '456', 
 '<EMAIL>', 
 '************', 
 'Mon-Fri 8am-4pm', 
 false, 
 true, 
 true, 
 false, 
 'Mr. Lee assists students with academic planning, course selection, and career guidance.', 
 'Academic Advising, Mentorship', 
 41.9960, 
 -81.5560),

-- Row 3: Staff – Orientation Coordinator
('Ms. Sandra Cooper', 
 'Orientation Coordinator', 
 'Orientation Programs', 
 'Student Affairs', 
 'New Student Services', 
 '', 
 'Orientation Center', 
 'Room 15', 
 'Fri 2pm-4pm', 
 '330-672-6200', 
 '789', 
 '<EMAIL>', 
 '330-672-6201', 
 'Mon-Fri 9am-5pm', 
 false, 
 true, 
 true, 
 false, 
 'Ms. Cooper manages orientation events and campus tours for incoming students, ensuring a smooth transition.', 
 'Orientation, Campus Tours', 
 41.9970, 
 -81.5570),

-- Row 4: Faculty – Dean of Graduate Studies
('Dr. Robert Williams', 
 'Dean of Graduate Studies', 
 'Graduate Studies', 
 'Graduate School', 
 'College of Arts & Sciences', 
 'Professional Studies', 
 'Administration Building', 
 'Room 301', 
 'Mon/Fri 11am-1pm', 
 '************', 
 '321', 
 '<EMAIL>', 
 '************', 
 'Mon-Fri 8am-6pm', 
 true, 
 false, 
 false, 
 true, 
 'Dr. Williams oversees graduate programs, promoting research excellence and academic leadership.', 
 'Graduate Program Administration, Mentorship', 
 41.9980, 
 -81.5580);

---------------------------
-- Table: thecollegeofwooster_currentstudents
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_currentstudents;
CREATE TABLE thecollegeofwooster_currentstudents (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INTEGER,
    department TEXT,
    building TEXT,
    room TEXT
);

INSERT INTO thecollegeofwooster_currentstudents (fullname, year, department, building, room) VALUES
('Alice Brown', 2, 'Biology', 'Science Building', 'Room 215'),
('Thomas Green', 3, 'History', 'Humanities Hall', 'Room 310'),
('Maria Lopez', 1, 'Economics', 'Academic Center', 'Room 112'),
('Daniel Kim', 4, 'Physics', 'Physics Lab', 'Room 415');
--------------------------------------

---------------------------
-- Table: thecollegeofwooster_housing
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_housing;
CREATE TABLE thecollegeofwooster_housing (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    hours TEXT,
    accessmethod TEXT,
    capacity INTEGER,
    residentorgorclub TEXT,
    phone TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_housing (fullname, hours, accessmethod, capacity, residentorgorclub, phone, about, latitude, longitude) VALUES
('Wooster Residential Hall', '24/7', 'Keycard', 300, 'Residential Life', '************', 'A traditional residence hall offering community living and student activities.', 41.9945, -81.5560),
('North Campus Apartments', '24/7', 'Electronic Entry', 150, 'Off-Campus Housing', '************', 'Modern apartment complexes designed for upperclassmen seeking independent living.', 41.9950, -81.5555),
('South Dormitory', '24/7', 'Smartphone App', 200, 'Residence Hall', '************', 'Cozy dormitory featuring study lounges, communal kitchens, and social events.', 41.9955, -81.5545),
('University Suites', '24/7', 'Biometric Access', 100, 'Student Organization', '************', 'Suite-style housing with private bedrooms and shared common areas for enhanced collaboration.', 41.9960, -81.5540);

---------------------------
-- Table: thecollegeofwooster_locallodging
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_locallodging;
CREATE TABLE thecollegeofwooster_locallodging (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_locallodging (fullname, hours, payment, phone, email, whatsapp, about, latitude, longitude) VALUES
('Wooster Guest House', '8am-10pm', 'Cash/Card', '************', '<EMAIL>', '************', 'A comfortable guest house offering overnight stays for visitors and parents during campus events.', 41.9970, -81.5575),
('Local Inn & Suites', '24/7 Lobby', 'Cash/Card/Online', '************', '<EMAIL>', '************', 'An inviting inn featuring modern amenities and proximity to campus.', 41.9975, -81.5580),
('Campus Lodge', '7am-11pm', 'Card/Online', '************', '<EMAIL>', '************', 'A budget-friendly option for families and conference attendees located within walking distance to campus.', 41.9980, -81.5585),
('Wooster Bed & Breakfast', '24/7 Reception', 'Cash/Card/Online', '************', '<EMAIL>', '************', 'A quaint bed and breakfast offering a home-like experience for short-term visitors.', 41.9985, -81.5590);
-------------------------------------


---------------------------
-- Table: thecollegeofwooster_shopsoreateries
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_shopsoreateries;
CREATE TABLE thecollegeofwooster_shopsoreateries (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    type TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    img_link TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_shopsoreateries (fullname, type, building, room, hours, payment, phone, email, fax, whatsapp, img_link, about, latitude, longitude) VALUES
('Campus Café', 'Café', 'Student Center', 'Lobby', '7am-7pm', 'Cash/Card', '************', '<EMAIL>', '************', '************', 'https://www.wooster.edu/images/campus_cafe.jpg', 'A popular spot for light snacks, coffee, and conversation.', 41.9950, -81.5565),
('Wooster Bookstore', 'Shop', 'Academic Center', 'First Floor', '8am-8pm', 'Cash/Card/Online', '************', '<EMAIL>', '************', '************', 'https://www.wooster.edu/images/bookstore.jpg', 'Offers textbooks, college merchandise, and school supplies.', 41.9945, -81.5570),
('The Deli & Market', 'Restaurant', 'South Hall', 'Room 102', '10am-6pm', 'Cash/Card', '************', '<EMAIL>', '************', '************', 'https://www.wooster.edu/images/deli.jpg', 'Fresh deli sandwiches and local produce with quick service.', 41.9940, -81.5580),
('Campus Convenience Store', 'Shop', 'Student Union', 'Ground Floor', '7am-11pm', 'Cash/Card', '330-672-8300', '<EMAIL>', '330-672-8301', '330-672-8302', 'https://www.wooster.edu/images/convenience_store.jpg', 'Offers snacks, beverages, and essentials for busy students.', 41.9935, -81.5585);

---------------------------
-- Table: thecollegeofwooster_mealplans
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_mealplans;
CREATE TABLE thecollegeofwooster_mealplans (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_mealplans (fullname, about) VALUES
('Standard Meal Plan', 'Provides access to campus dining halls with balanced meal options for undergraduates.'),
('Premium Meal Plan', 'Includes unlimited access to dining facilities, specialty cafes, and guest passes.'),
('Budget Meal Plan', 'A cost-effective plan providing essential meals for students on a tight budget.'),
('Athlete Meal Plan', 'Customized for student-athletes with enhanced nutritional support and flexible scheduling.');

---------------------------
-- Table: thecollegeofwooster_localareadining
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_localareadining;
CREATE TABLE thecollegeofwooster_localareadining (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    img_link TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_localareadining (fullname, hours, payment, phone, email, whatsapp, img_link, about, latitude, longitude) VALUES
('Downtown Bistro', '11am-11pm', 'Cash/Card', '************', '<EMAIL>', '************', 'https://www.wooster.edu/images/bistro.jpg', 'A local bistro offering seasonal menus and a cozy ambiance.', 41.9920, -81.5590),
('The Gourmet Grill', '12pm-10pm', 'Cash/Card/Online', '************', '<EMAIL>', '************', 'https://www.wooster.edu/images/gourmet_grill.jpg', 'Upscale dining with a focus on locally sourced ingredients.', 41.9915, -81.5595),
('Neighborhood Pizzeria', '10am-10pm', 'Cash/Card', '************', '<EMAIL>', '************', 'https://www.wooster.edu/images/pizzeria.jpg', 'Casual pizza joint popular with students and locals alike.', 41.9910, -81.5600),
('Cafe Artisan', '8am-8pm', 'Cash/Card', '************', '<EMAIL>', '************', 'https://www.wooster.edu/images/cafe_artisan.jpg', 'A quaint cafe serving artisanal coffee, pastries, and light meals.', 41.9905, -81.5605);

---------------------------
-- Table: thecollegeofwooster_studentdiscounts
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_studentdiscounts;
CREATE TABLE thecollegeofwooster_studentdiscounts (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_studentdiscounts (fullname, hours, payment, phone, email, whatsapp, about, latitude, longitude) VALUES
('Campus Cinema Discount', 'Daily 10am-10pm', 'Valid Student ID', '************', '<EMAIL>', '************', 'Discounted movie tickets for students showing a valid ID.', 41.9900, -81.5610),
('Bookstore Savings', 'Mon-Fri 9am-5pm', 'Student Discount', '************', '<EMAIL>', '************', 'Exclusive discounts on textbooks and campus merchandise.', 41.9895, -81.5615),
('Fitness Center Student Pass', '24/7', 'Membership Card', '************', '<EMAIL>', '************', 'Special membership rates and classes available for enrolled students.', 41.9890, -81.5620),
('Local Diner Discount', '11am-9pm', 'Student ID Required', '************', '<EMAIL>', '************', 'Enjoy reduced prices at this popular local diner with your student ID.', 41.9885, -81.5625);

---------------------------
-- Table: thecollegeofwooster_inventory
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_inventory;
CREATE TABLE thecollegeofwooster_inventory (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    price NUMERIC,
    seller TEXT,
    category TEXT,
    onsale BOOLEAN,
    about TEXT,
    originalprice NUMERIC
);

INSERT INTO thecollegeofwooster_inventory (fullname, price, seller, category, onsale, about, originalprice) VALUES
('Textbook: Intro to Biology', 120.00, 'Campus Bookstore', 'Books', true, 'A comprehensive introduction to biological concepts.', 150.00),
('Wooster Hoodie', 45.00, 'Campus Store', 'Apparel', false, 'Classic college hoodie with embroidered logo.', 45.00),
('Wireless Mouse', 25.00, 'Tech Shop', 'Electronics', true, 'Ergonomic wireless mouse suitable for student use.', 35.00),
('Notebook Pack', 10.00, 'Campus Store', 'Stationery', false, 'Pack of 5 academic notebooks for daily use.', 10.00);

---------------------------
-- Table: thecollegeofwooster_menus
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_menus;
CREATE TABLE thecollegeofwooster_menus (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    price NUMERIC,
    dininglocation TEXT,
    diningstation TEXT,
    breakfast TEXT,
    lunch TEXT,
    dinner TEXT,
    _mon TEXT,
    _tue TEXT,
    _wed TEXT,
    _thur TEXT,
    _fri TEXT,
    _sat TEXT,
    _sun TEXT,
    about TEXT,
    allergens TEXT,
    ingredients TEXT
);

INSERT INTO thecollegeofwooster_menus (fullname, price, dininglocation, diningstation, breakfast, lunch, dinner, _mon, _tue, _wed, _thur, _fri, _sat, _sun, about, allergens, ingredients) VALUES
('Healthy Start Menu', 8.50, 'Main Dining Hall', 'Breakfast Station', 'Oatmeal with fruit', 'Grilled chicken salad', 'Salmon with quinoa', 'Eggs, oatmeal', 'Fruit bowl', 'Yogurt parfait', 'Smoothie', 'Eggs, toast', 'Pancakes', 'Bagel', 'A balanced menu offering nutritious choices for early risers.', 'Gluten, Nuts', 'Oats, Milk, Chicken, Salmon, Quinoa, Fruits'),
('Veggie Delight Menu', 7.00, 'North Dining Center', 'Vegetarian Station', 'Avocado toast', 'Veggie wrap', 'Pasta primavera', 'Tofu scramble', 'Salad bowl', 'Veggie burger', 'Grilled veggies', 'Wrap', 'Pasta', 'Soup', 'A menu crafted for vegetarians with fresh, seasonal produce.', 'Gluten, Dairy', 'Avocado, Bread, Tofu, Vegetables, Pasta'),
('Classic Diner Menu', 9.00, 'South Campus Eatery', 'Diner Station', 'Pancakes and bacon', 'Club sandwich', 'Steak and potatoes', 'Omelette', 'Sandwich', 'Burger', 'Steak', 'Fries', 'Soda', 'Coffee', 'Offers classic American diner favorites.', 'Dairy, Meat', 'Eggs, Bacon, Bread, Steak, Potatoes'),
('International Fusion Menu', 10.00, 'International Dining Hall', 'Fusion Station', 'Chilaquiles', 'Sushi platter', 'Curry with rice', 'Miso soup', 'Tacos', 'Sushi', 'Curry', 'Noodles', 'Dim sum', 'Ramen', 'A diverse menu blending flavors from around the world.', 'Seafood, Soy', 'Corn, Rice, Seaweed, Curry spices, Soy sauce');


----------------------------------

---------------------------
-- Table: thecollegeofwooster_campusshuttle
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_campusshuttle;
CREATE TABLE thecollegeofwooster_campusshuttle (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    fax TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_campusshuttle (fullname, phone, email, whatsapp, fax, latitude, longitude, about) VALUES
('Campus Shuttle Blue', '************', '<EMAIL>', '************', '************', 41.9955, -81.5560, 'Provides frequent service between residence halls and academic buildings.'),
('Campus Shuttle Green', '************', '<EMAIL>', '************', '************', 41.9960, -81.5565, 'Eco-friendly shuttle serving main campus routes and off-campus housing.'),
('Campus Shuttle Red', '************', '<EMAIL>', '************', '************', 41.9965, -81.5570, 'Shuttle service designed for late-evening trips and weekend events.');

---------------------------
-- Table: thecollegeofwooster_parkingspaces
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_parkingspaces;
CREATE TABLE thecollegeofwooster_parkingspaces (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    hours TEXT,
    payment TEXT,
    capacity INTEGER,
    phone TEXT,
    whatsapp TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    building TEXT,
    building2 TEXT,
    building3 TEXT,
    building4 TEXT,
    building5 TEXT,
    building6 TEXT,
    building7 TEXT,
    studentparking BOOLEAN,
    facultyorstaffparking BOOLEAN,
    about TEXT
);

INSERT INTO thecollegeofwooster_parkingspaces (fullname, hours, payment, capacity, phone, whatsapp, latitude, longitude, building, building2, building3, building4, building5, building6, building7, studentparking, facultyorstaffparking, about) VALUES
('North Campus Parking Lot', '24/7', 'Cash/Card/Online', 150, '************', '************', 41.9940, -81.5580, 'Academic Center', 'Library', 'Science Building', 'Student Center', NULL, NULL, NULL, true, false, 'Secure parking lot near multiple academic buildings for student use.'),
('South Campus Parking Garage', '6am-11pm', 'Card Only', 200, '************', '************', 41.9930, -81.5590, 'Administration Building', 'Athletic Center', 'Residence Hall A', 'Residence Hall B', 'Dining Hall', NULL, NULL, false, true, 'Multi-level garage with reserved spots for faculty and staff.'),
('Central Parking Area', '24/7', 'Cash/Card', 100, '************', '************', 41.9920, -81.5600, 'Student Union', 'Academic Center', NULL, NULL, NULL, NULL, NULL, true, true, 'Common parking area serving both students and campus employees.');

---------------------------
-- Table: thecollegeofwooster_localtransport
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_localtransport;
CREATE TABLE thecollegeofwooster_localtransport (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    fax TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_localtransport (fullname, payment, phone, email, whatsapp, fax, latitude, longitude, about) VALUES
('Wooster Transit Lines', 'Cash/Card/Online', '************', '<EMAIL>', '************', '************', 41.9910, -81.5615, 'Local bus service connecting campus with surrounding communities.'),
('Campus Taxi Service', 'Card/Online', '************', '<EMAIL>', '************', '************', 41.9905, -81.5620, 'On-demand taxi service available to students and staff for short trips.'),
('Regional Rideshare', 'Mobile Payment', '************', '<EMAIL>', '************', '************', 41.9900, -81.5625, 'Affordable rideshare options for commuting in the local area.');


-----------------------------------
---------------------------
-- Table: thecollegeofwooster_schools
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_schools;
CREATE TABLE thecollegeofwooster_schools (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_schools (fullname, building, room, phone, email, fax, whatsapp, latitude, longitude, about) VALUES
('College of Arts & Sciences', 'Arts Building', 'Room 101', '************', '<EMAIL>', '************', '************', 41.9940, -81.5570, 'The hub for humanities, social sciences, and natural sciences.'),
('College of Engineering', 'Engineering Complex', 'Room 202', '************', '<EMAIL>', '************', '************', 41.9935, -81.5575, 'Focuses on innovative engineering and technology programs.'),
('College of Business', 'Business Center', 'Room 303', '************', '<EMAIL>', '************', '************', 41.9930, -81.5580, 'Offers programs in management, finance, and marketing.'),
('College of Education', 'Education Hall', 'Room 404', '************', '<EMAIL>', '************', '************', 41.9925, -81.5585, 'Prepares future educators with a focus on experiential learning.');

---------------------------
-- Table: thecollegeofwooster_departments
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_departments;
CREATE TABLE thecollegeofwooster_departments (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    school TEXT,
    school2 TEXT,
    school3 TEXT,
    alumnidepartment TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_departments (fullname, building, room, payment, phone, email, fax, whatsapp, school, school2, school3, alumnidepartment, about, latitude, longitude) VALUES
('Department of Biology', 'Science Building', 'B210', 'None', '************', '<EMAIL>', '************', '************', 'College of Arts & Sciences', NULL, NULL, 'Biology Alumni', 'Offers undergraduate and graduate programs in biological sciences and research opportunities.', 41.9945, -81.5575),
('Department of History', 'History Hall', 'C101', 'None', '************', '<EMAIL>', '************', '************', 'College of Arts & Sciences', NULL, NULL, 'History Alumni', 'Provides a rich curriculum in world history and cultural studies.', 41.9940, -81.5580),
('Department of Mechanical Engineering', 'Engineering Complex', 'E305', 'None', '************', '<EMAIL>', '************', '************', 'College of Engineering', NULL, NULL, 'Engineering Alumni', 'A leader in mechanical innovation, design, and sustainable engineering solutions.', 41.9935, -81.5585),
('Department of Business Administration', 'Business Center', 'B202', 'None', '330-672-1230', '<EMAIL>', '************', '************', 'College of Business', NULL, NULL, 'Business Alumni', 'Provides a comprehensive curriculum in management, finance, and entrepreneurial studies.', 41.9930, -81.5590);

---------------------------
-- Table: thecollegeofwooster_centers
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_centers;
CREATE TABLE thecollegeofwooster_centers (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    payment TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_centers (fullname, building, room, hours, payment, phone, email, fax, whatsapp, latitude, longitude, about) VALUES
('Center for Research and Innovation', 'Innovation Center', 'Room 10', 'Mon-Fri 8am-6pm', 'None', '************', '<EMAIL>', '************', '************', 41.9920, -81.5595, 'Fosters interdisciplinary research and creative problem-solving initiatives.'),
('Center for Student Success', 'Student Center', 'Room 22', 'Mon-Fri 9am-5pm', 'None', '************', '<EMAIL>', '************', '************', 41.9915, -81.5600, 'Provides academic advising, tutoring, and career counseling services.'),
('Center for Global Studies', 'Global Center', 'Room 5', 'Mon-Fri 8am-6pm', 'None', '************', '<EMAIL>', '************', '************', 41.9910, -81.5605, 'Promotes international programs, study abroad, and multicultural understanding.'),
('Center for Community Engagement', 'Community Hall', 'Room 18', 'Mon-Fri 8am-5pm', 'None', '************', '<EMAIL>', '************', '************', 41.9905, -81.5610, 'Encourages partnerships between the college and local community organizations.');

---------------------------
-- Table: thecollegeofwooster_documents
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_documents;
CREATE TABLE thecollegeofwooster_documents (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    link TEXT,
    admissionsdocument TEXT,
    graduationdocument TEXT,
    statsdocument TEXT,
    statsfinancial TEXT,
    year INTEGER,
    major TEXT,
    department TEXT,
    school TEXT
);

INSERT INTO thecollegeofwooster_documents (fullname, link, admissionsdocument, graduationdocument, statsdocument, statsfinancial, year, major, department, school) VALUES
('Undergraduate Admissions Guide', 'https://www.wooster.edu/docs/admissions_guide.pdf', 'Yes', 'No', 'No', 'No', 2025, 'All', 'Admissions', 'College of Arts & Sciences'),
('Graduation Requirements Bulletin', 'https://www.wooster.edu/docs/graduation_requirements.pdf', 'No', 'Yes', 'No', 'No', 2025, 'All', 'Academic Affairs', 'College of Arts & Sciences'),
('Enrollment Statistics Report', 'https://www.wooster.edu/docs/enrollment_stats.pdf', 'No', 'No', 'Yes', 'No', 2024, 'N/A', 'Institutional Research', 'College of Arts & Sciences'),
('Financial Overview 2024', 'https://www.wooster.edu/docs/financial_overview.pdf', 'No', 'No', 'No', 'Yes', 2024, 'N/A', 'Finance', 'College of Business');

------------------------------------
---------------------------
-- Table: thecollegeofwooster_majors
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_majors;
CREATE TABLE thecollegeofwooster_majors (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    department TEXT,
    department2 TEXT,
    department3 TEXT,
    school TEXT,
    school2 TEXT,
    school3 TEXT,
    duration TEXT,
    about TEXT,
    whoitsfor TEXT,
    goalsandobjectives TEXT,
    aims TEXT,
    careerprospects TEXT,
    learningoutcomes TEXT,
    learningmethods TEXT,
    assessment TEXT,
    studentsupport TEXT,
    accredited BOOLEAN
);

INSERT INTO thecollegeofwooster_majors (
    fullname, department, department2, department3, school, school2, school3,
    duration, about, whoitsfor, goalsandobjectives, aims, careerprospects,
    learningoutcomes, learningmethods, assessment, studentsupport, accredited
) VALUES
('Biology', 'Department of Biology', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '4 years', 'Focuses on the study of living organisms, from molecular biology to ecology.',
 'Students interested in life sciences, research, or healthcare careers.',
 'Develop scientific knowledge, research skills, and critical thinking.',
 'Prepare students for graduate study, medicine, or biological careers.',
 'Medicine, research, biotechnology, education.',
 'Understanding of cellular, organismal, and ecological biology.',
 'Lectures, labs, research projects.',
 'Exams, lab reports, independent study.',
 'Academic advising, tutoring, research mentoring.', TRUE),

('International Relations', 'Department of Political Science', 'Department of History', NULL, 'College of Arts & Sciences', NULL, NULL,
 '4 years', 'Examines global political systems, diplomacy, and conflict.',
 'Students with an interest in politics, history, and global issues.',
 'Analyze global affairs and develop diplomatic strategies.',
 'Promote global understanding and engagement.',
 'Foreign service, NGOs, law, policy analysis.',
 'Critical understanding of global institutions and policies.',
 'Seminars, simulations, study abroad.',
 'Essays, debates, presentations.',
 'Advising, international internship guidance.', TRUE),

('Computer Science', 'Department of Computer Science', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '4 years', 'Provides a foundation in computing theory, systems, and programming.',
 'Students interested in software, AI, or data science.',
 'Train students in algorithms, data structures, and software development.',
 'Foster innovation and problem-solving using computing tools.',
 'Tech industry, AI, game design, cybersecurity.',
 'Proficiency in coding, systems, and problem-solving.',
 'Projects, lectures, internships.',
 'Programming assignments, exams, capstone.',
 'Career services, peer mentoring, hackathons.', TRUE),

('Music', 'Department of Music', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '4 years', 'Explores music theory, history, and performance.',
 'Musicians, composers, and those passionate about sound and performance.',
 'Enhance technical skills and artistic expression.',
 'Cultivate musicianship and music appreciation.',
 'Performance, composition, music therapy, education.',
 'Demonstrate theory and performance proficiency.',
 'Ensemble participation, studio instruction.',
 'Juries, recitals, portfolios.',
 'Practice spaces, faculty mentorship, performance advising.', TRUE);

---------------------------
-- Table: thecollegeofwooster_minors
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_minors;
CREATE TABLE thecollegeofwooster_minors (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    department TEXT,
    department2 TEXT,
    department3 TEXT,
    school TEXT,
    school2 TEXT,
    school3 TEXT,
    duration TEXT,
    about TEXT,
    whoitsfor TEXT,
    goalsandobjectives TEXT,
    aims TEXT,
    careerprospects TEXT,
    learningoutcomes TEXT,
    learningmethods TEXT,
    assessment TEXT,
    studentsupport TEXT,
    accredited BOOLEAN
);

INSERT INTO thecollegeofwooster_minors (
    fullname, department, department2, department3, school, school2, school3,
    duration, about, whoitsfor, goalsandobjectives, aims, careerprospects,
    learningoutcomes, learningmethods, assessment, studentsupport, accredited
) VALUES
('Africana Studies', 'Africana Studies Program', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '2 years', 'Focuses on the history, culture, and politics of African and African-descended peoples.',
 'Students interested in cultural studies and social justice.',
 'Promote awareness of the African diaspora.',
 'Empower critical exploration of race and identity.',
 'Law, education, public policy, journalism.',
 'Understanding historical and contemporary African cultures.',
 'Discussion, text analysis, research.',
 'Essays, presentations, projects.',
 'Advising and faculty mentorship.', TRUE),

('Statistics', 'Mathematics & Statistics Department', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '2 years', 'Provides tools for data analysis and decision-making.',
 'STEM and social science students needing quantitative skills.',
 'Train in data interpretation and statistical methods.',
 'Support data-driven research across disciplines.',
 'Business, economics, data science.',
 'Analyze and present data effectively.',
 'Hands-on labs, lectures, projects.',
 'Problem sets, exams, data projects.',
 'Math lab, tutoring, advising.', TRUE),

('Art History', 'Department of Art and Art History', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '2 years', 'Studies the history and context of art across cultures.',
 'Students interested in museums, education, or visual culture.',
 'Understand art movements and critical theories.',
 'Develop visual literacy and cultural knowledge.',
 'Museum work, curating, publishing.',
 'Evaluate and discuss visual media.',
 'Image analysis, papers, gallery visits.',
 'Research papers, oral presentations.',
 'Museum visits, critique workshops.', TRUE),

('East Asian Studies', 'East Asian Studies Program', NULL, NULL, 'College of Arts & Sciences', NULL, NULL,
 '2 years', 'Explores history, language, and culture of East Asia.',
 'Students interested in global studies or Asian affairs.',
 'Gain cultural and linguistic competency.',
 'Understand the complexities of East Asia.',
 'International business, government, translation.',
 'Appreciate diverse East Asian societies.',
 'Language learning, cultural immersion.',
 'Language exams, cultural essays.',
 'Language tables, study abroad support.', TRUE);

-----------------------------------------------------
-- Table: thecollegeofwooster_funding
-----------------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_funding;
CREATE TABLE thecollegeofwooster_funding (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    major TEXT
);

INSERT INTO thecollegeofwooster_funding (fullname, about, major) VALUES
('Wooster Independent Study Research Funding', 
 'Provides financial support to students undertaking Independent Study (I.S.) research, including travel, materials, and equipment costs.', 
 'Anthropology'),

('STEM Research Grant', 
 'Offered to students pursuing majors in science, technology, engineering, or mathematics to fund laboratory or fieldwork research.', 
 'Biology'),

('Copeland Fund for Independent Study', 
 'Supports expenses related to senior I.S. projects, including archival research, creative work, or data collection in the field.', 
 'History'),

('Mellon Foundation Grant', 
 'Supports humanities and social science research, with emphasis on interdisciplinary and community-focused projects.', 
 'Philosophy');


DROP TABLE IF EXISTS thecollegeofwooster_coursecatalog;
CREATE TABLE thecollegeofwooster_coursecatalog (
    id SERIAL PRIMARY KEY,
    modulecode TEXT,
    modulename TEXT,
    about TEXT,
    major TEXT,
    minor TEXT,
    school TEXT,
    school2 TEXT,
    school3 TEXT,
    department TEXT,
    department2 TEXT,
    department3 TEXT,
    prerequisites TEXT,
    corequisites TEXT,
    corecourse BOOLEAN,
    minorrequirement BOOLEAN,
    electivecourse BOOLEAN,
    year INTEGER,
    term TEXT,
    credits INTEGER,
    classesperweek INTEGER,
    labhoursperweek INTEGER,
    tutorialhoursperweek INTEGER,
    aim TEXT,
    learningoutcomes TEXT,
    assessment TEXT,
    substitutemodulecode TEXT,
    substitutemodulename TEXT,
    offered BOOLEAN
);

INSERT INTO thecollegeofwooster_coursecatalog (
    modulecode, modulename, about, major, minor, school, department, prerequisites, corequisites,
    corecourse, minorrequirement, electivecourse, year, term, credits, classesperweek,
    labhoursperweek, tutorialhoursperweek, aim, learningoutcomes, assessment,
    substitutemodulecode, substitutemodulename, offered
) VALUES
('CHEM101', 'General Chemistry I', 'Introduction to chemical principles, including atomic theory and bonding.', 'Chemistry', 'Biology', 'Natural Sciences', 'Chemistry', '', '', TRUE, FALSE, TRUE, 1, 'Fall', 4, 3, 1, 1, 'To introduce foundational concepts in chemistry.', 'Understand atomic structure, bonding, and basic reactions.', 'Exams, lab reports, participation.', NULL, NULL, TRUE),
('HIST210', 'Modern European History', 'A survey of European history from 1789 to the present.', 'History', 'Political Science', 'Social Sciences', 'History', '', '', FALSE, TRUE, TRUE, 2, 'Spring', 4, 3, 0, 1, 'Explore major events and ideas in modern Europe.', 'Analyze historical trends and causes.', 'Essays, exams.', NULL, NULL, TRUE),
('PSCI120', 'Intro to U.S. Politics', 'Examines the structure and function of the U.S. government.', 'Political Science', '', 'Social Sciences', 'Political Science', '', '', TRUE, FALSE, FALSE, 1, 'Fall', 4, 3, 0, 1, 'Familiarize students with American political systems.', 'Identify roles of branches and policymaking.', 'Exams, papers.', NULL, NULL, TRUE),
('ENGW110', 'College Writing', 'Focus on developing writing skills across academic disciplines.', 'English', '', 'Humanities', 'English', '', '', TRUE, FALSE, TRUE, 1, 'Fall', 4, 3, 0, 1, 'Improve academic writing and research techniques.', 'Write effectively and persuasively.', 'Essays, peer review, presentations.', NULL, NULL, TRUE);

DROP TABLE IF EXISTS thecollegeofwooster_enrollmentexercise;
CREATE TABLE thecollegeofwooster_enrollmentexercise (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    major TEXT
);

INSERT INTO thecollegeofwooster_enrollmentexercise (fullname, about, major) VALUES
('Intro to Biology - Lab Registration', 'Exercise simulating lab course registration, capacity limits, and waitlisting.', 'Biology'),
('Computer Science Enrollment Drill', 'Practice course enrollment using a mock CS curriculum and time slots.', 'Computer Science'),
('Psychology Course Mapping', 'Map out a sample four-year plan using psychology electives and core courses.', 'Psychology'),
('Economics Prerequisite Tree', 'Students trace through course prerequisites to enroll in advanced economics electives.', 'Economics');


DROP TABLE IF EXISTS thecollegeofwooster_academicresources;
CREATE TABLE thecollegeofwooster_academicresources (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude DECIMAL,
    longitude DECIMAL
);

INSERT INTO thecollegeofwooster_academicresources (
    fullname, building, room, hours, phone, email, fax, whatsapp, about, latitude, longitude
) VALUES
('Andrews Library', 'Andrews Library', '100', 'Mon–Fri 8am–12am', '************', '<EMAIL>', NULL, NULL, 'Central academic library with research and writing support.', 40.8077, -81.9331),
('Math Center', 'Taylor Hall', '215', 'Mon–Fri 9am–5pm', '************', '<EMAIL>', NULL, NULL, 'Support for calculus, statistics, and quantitative courses.', 40.8080, -81.9345),
('Writing Center', 'Freel Library', 'B10', 'Mon–Thu 10am–8pm, Sun 4–8pm', '************', '<EMAIL>', NULL, NULL, 'Assistance with writing and peer editing.', 40.8079, -81.9322),
('STEM Zone', 'Wishart Hall', '001', 'Mon–Fri 10am–6pm', '************', '<EMAIL>', NULL, NULL, 'Tutoring and support for STEM disciplines.', 40.8082, -81.9335);


DROP TABLE IF EXISTS thecollegeofwooster_academichonors;
CREATE TABLE thecollegeofwooster_academichonors (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    major TEXT
);

INSERT INTO thecollegeofwooster_academichonors (fullname, about, major) VALUES
('Deans List', 'Awarded to students with a GPA of 3.65 or higher in a given semester.', 'All Majors'),
('Phi Beta Kappa', 'National honor society recognizing outstanding academic achievement in the liberal arts.', 'Liberal Arts'),
('Senior I.S. Honors', 'Given for exemplary Independent Study (I.S.) thesis work.', 'Various Majors'),
('First-Year Scholar Award', 'Recognizes academic excellence among first-year students.', 'Undeclared');


DROP TABLE IF EXISTS thecollegeofwooster_academicprizes;
CREATE TABLE thecollegeofwooster_academicprizes (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_academicprizes (fullname, about) VALUES
('James R. Turner Prize in Economics', 'Awarded for outstanding achievement in the field of Economics.'),
('Paul F. Brown Music Prize', 'Recognizes excellence in performance or composition in Music.'),
('William E. Bricker Prize in Philosophy', 'Given to a senior demonstrating philosophical depth and clarity.'),
('First-Year Writing Prize', 'Best writing submission by a first-year student at Wooster.');


DROP TABLE IF EXISTS thecollegeofwooster_academicdress;
CREATE TABLE thecollegeofwooster_academicdress (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    category TEXT,
    style TEXT,
    color TEXT
);

INSERT INTO thecollegeofwooster_academicdress (fullname, category, style, color) VALUES
('Bachelors Gown', 'Undergraduate', 'Open-sleeved robe', 'Black'),
('Masters Gown', 'Graduate', 'Closed-sleeved robe with oblong sleeves', 'Black'),
('Doctoral Regalia', 'Doctorate', 'Velvet panels and three stripes on sleeves', 'Black and Gold'),
('Hood - Arts & Humanities', 'Hood', '3.5 ft length with velvet trim', 'White and Red');





---------------------------
-- Table: thecollegeofwooster_entryrequirements
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_entryrequirements;
CREATE TABLE thecollegeofwooster_entryrequirements (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    major TEXT
);

INSERT INTO thecollegeofwooster_entryrequirements (fullname, about, major) VALUES
('Standard First-Year Admission', 'Applicants should have a strong college-preparatory curriculum, including 4 years of English, 3+ years of math, social studies, and lab sciences.', 'All Majors'),
('SAT/ACT Optional Policy', 'Submission of standardized test scores is optional and not required for admission.', 'All Majors'),
('Portfolio Review for Music Majors', 'Students applying to the Music program must submit a performance video or arrange a live audition.', 'Music'),
('Pre-Engineering Requirements', 'Strong preparation in calculus, physics, and chemistry is required.', 'Pre-Engineering');


---------------------------
-- Table: thecollegeofwooster_gradingscale
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_gradingscale;
CREATE TABLE thecollegeofwooster_gradingscale (
    id SERIAL PRIMARY KEY,
    mark TEXT,
    lettergrade TEXT,
    graderemark TEXT,
    programlevel TEXT
);

INSERT INTO thecollegeofwooster_gradingscale (mark, lettergrade, graderemark, programlevel) VALUES
('93-100', 'A', 'Excellent', 'Undergraduate'),
('90-92', 'A-', 'Very Good', 'Undergraduate'),
('87-89', 'B+', 'Good', 'Undergraduate'),
('83-86', 'B', 'Good', 'Undergraduate'),
('80-82', 'B-', 'Satisfactory', 'Undergraduate'),
('77-79', 'C+', 'Satisfactory', 'Undergraduate'),
('73-76', 'C', 'Average', 'Undergraduate'),
('70-72', 'C-', 'Below Average', 'Undergraduate'),
('60-69', 'D', 'Marginal Pass', 'Undergraduate'),
('Below 60', 'F', 'Failing', 'Undergraduate');
--------------------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_programs;
CREATE TABLE thecollegeofwooster_programs (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_programs (fullname, phone, email, whatsapp, about) VALUES
('Global Engagement Program', '************', '<EMAIL>', '+13302632500', 'Offers students opportunities for international study, internships, and research.'),
('STEM Scholars Program', '************', '<EMAIL>', '+13302632100', 'Provides academic and career support for students in science, technology, engineering, and math fields.'),
('Independent Study Program', '************', '<EMAIL>', '+13302632000', 'Wooster’s capstone research program allowing students to conduct faculty-mentored original research.'),
('Center for Diversity and Inclusion', '************', '<EMAIL>', '+13302632050', 'Promotes inclusive programs, support services, and initiatives to foster community and equity.');


DROP TABLE IF EXISTS thecollegeofwooster_signatureevents;
CREATE TABLE thecollegeofwooster_signatureevents (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    alumnisignatureevent BOOLEAN,
    about TEXT
);

INSERT INTO thecollegeofwooster_signatureevents (fullname, alumnisignatureevent, about) VALUES
('Independent Study Symposium', FALSE, 'Annual event where seniors present their I.S. research to the campus and public.'),
('Alumni Weekend', TRUE, 'A weekend for alumni to return to campus, reconnect with peers, and attend special programming.'),
('Black & Gold Weekend', TRUE, 'Campus-wide celebration featuring athletic events, performances, and community gatherings.'),
('Scot Spirit Day', FALSE, 'Day of service and celebration of Wooster pride, featuring volunteer projects and campus-wide festivities.');


DROP TABLE IF EXISTS thecollegeofwooster_traditions;
CREATE TABLE thecollegeofwooster_traditions (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_traditions (fullname, about) VALUES
('Arch Sing', 'Seniors gather under Kauke Arch the night before graduation to sing their favorite campus songs.'),
('Bell Tower Climb', 'Graduating seniors ring the bell in Kauke Hall as a symbol of their Wooster journey.'),
('Tartan Walk', 'Students walk across campus in plaid regalia to celebrate the beginning and end of their Wooster experience.'),
('I.S. Monday Parade', 'On the day senior I.S. papers are due, students celebrate by parading through campus in costumes.');






---------------------------
-- Table: thecollegeofwooster_partnershipopportunities
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_partnershipopportunities;
CREATE TABLE thecollegeofwooster_partnershipopportunities (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_partnershipopportunities (fullname, phone, email, whatsapp, about) VALUES
('Community-Based Learning Collaborations', '************', '<EMAIL>', NULL, 'Partnerships between faculty, students, and local organizations to solve real-world problems.'),
('Internship Host Opportunities', '************', '<EMAIL>', NULL, 'Companies and organizations can host students for summer internships through APEX.'),
('Research Sponsorships', '************', '<EMAIL>', NULL, 'Organizations may co-sponsor student research or provide mentorship.'),
('Study Abroad Partnerships', '************', '<EMAIL>', NULL, 'Institutions interested in student or faculty exchanges can contact the Global Engagement Office.');
---------------------------------------------

---------------------------
-- Table: thecollegeofwooster_athletics
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_athletics;
CREATE TABLE thecollegeofwooster_athletics (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_athletics (fullname, phone, email, whatsapp, about, link) VALUES
('Men’s Basketball', '************', '<EMAIL>', NULL, 'One of the most successful NCAA Division III basketball programs.', 'https://woosterathletics.com/sports/mbkb/index'),
('Women’s Soccer', '************', '<EMAIL>', NULL, 'Competes in the NCAC with a strong academic-athletic balance.', 'https://woosterathletics.com/sports/wsoc/index'),
('Swimming and Diving', '************', '<EMAIL>', NULL, 'Offers competitive opportunities in a team-focused environment.', 'https://woosterathletics.com/sports/mswimdive/index'),
('Track and Field', '************', '<EMAIL>', NULL, 'Indoor and outdoor seasons with emphasis on growth and performance.', 'https://woosterathletics.com/sports/track/index');


DROP TABLE IF EXISTS thecollegeofwooster_orgsorclubs;
CREATE TABLE thecollegeofwooster_orgsorclubs (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_orgsorclubs (fullname, phone, email, about) VALUES
('Wooster Activities Crew (WAC)', '************', '<EMAIL>', 'Organizes campus-wide social, cultural, and recreational events.'),
('Black Student Association (BSA)', '************', '<EMAIL>', 'Promotes cultural awareness, inclusion, and advocacy for Black students.'),
('Student Government Association (SGA)', '************', '<EMAIL>', 'Serves as the student voice in campus governance and policy discussions.'),
('Wooster Music Collective', '************', '<EMAIL>', 'Supports student-led music projects, concerts, and jam sessions.');


DROP TABLE IF EXISTS thecollegeofwooster_researchgroups;
CREATE TABLE thecollegeofwooster_researchgroups (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_researchgroups (fullname, phone, email, about) VALUES
('Environmental Research Group', '************', '<EMAIL>', 'Conducts interdisciplinary studies on sustainability, climate change, and ecology.'),
('Data Analytics Lab', '************', '<EMAIL>', 'Focuses on machine learning, big data, and statistical modeling across disciplines.'),
('Social Justice Research Collaborative', '************', '<EMAIL>', 'Engages in research projects that explore race, equity, and social policy.'),
('Molecular Biology Research Team', '************', '<EMAIL>', 'Investigates genetic and cellular mechanisms through lab-based research.');


DROP TABLE IF EXISTS thecollegeofwooster_committees;
CREATE TABLE thecollegeofwooster_committees (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_committees (fullname, phone, email, about) VALUES
('Academic Planning Committee', '************', '<EMAIL>', 'Reviews and recommends academic policies and programmatic changes.'),
('Sustainability Committee', '************', '<EMAIL>', 'Guides campus sustainability efforts and green initiatives.'),
('Diversity, Equity, and Inclusion Committee', '************', '<EMAIL>', 'Works on policy and practice for campus-wide diversity and inclusion.'),
('Student Life Committee', '************', '<EMAIL>', 'Addresses co-curricular programming, student support, and campus life matters.');



-------------------------
DROP TABLE IF EXISTS thecollegeofwooster_news;
CREATE TABLE thecollegeofwooster_news (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    day INT,
    month INT,
    year INT,
    publisher TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_news (fullname, day, month, year, publisher, link) VALUES
('Wooster Announces 2025 Independent Study Symposium', 15, 3, 2025, 'Office of Communications', 'https://news.wooster.edu/independent-study-symposium-2025'),
('College of Wooster Launches New Sustainability Initiative', 20, 2, 2025, 'Wooster Newsroom', 'https://news.wooster.edu/sustainability-initiative'),
('Alumna Jane Doe Honored with National Science Medal', 28, 1, 2025, 'Alumni Affairs', 'https://news.wooster.edu/jane-doe-award'),
('Wooster Theatre Department Premieres Original Production', 5, 4, 2025, 'Campus Life', 'https://news.wooster.edu/theatre-premiere-2025');

DROP TABLE IF EXISTS thecollegeofwooster_periodicals;
CREATE TABLE thecollegeofwooster_periodicals (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_periodicals (fullname, phone, email, whatsapp, about) VALUES
('The Wooster Voice', '************', '<EMAIL>', '+13302630001', 'The official student-run newspaper covering campus news, opinion, arts, and sports.'),
('Artful Mind', '************', '<EMAIL>', '+13302630011', 'A quarterly student literary magazine featuring poetry, essays, and visual art.'),
('Wooster Science Review', '************', '<EMAIL>', '+13302630022', 'An academic publication of undergraduate science research at The College of Wooster.');

DROP TABLE IF EXISTS thecollegeofwooster_radio;
CREATE TABLE thecollegeofwooster_radio (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_radio (fullname, phone, email, whatsapp, about) VALUES
('WCWS 90.9 FM (WOO 91)', '************', '<EMAIL>', '+13302632100', 'Student-run radio station broadcasting music, talk shows, and campus updates.'),
('The SoundLab', '************', '<EMAIL>', '+13302632150', 'Experimental and student-led audio broadcast exploring podcasting and indie radio.');

DROP TABLE IF EXISTS thecollegeofwooster_television;
CREATE TABLE thecollegeofwooster_television (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_television (fullname, phone, email, whatsapp, about) VALUES
('ScotTV', '************', '<EMAIL>', '+13302632200', 'The College of Wooster’s student-run television network showcasing campus events, interviews, and creative content.'),
('Independent Study Showcase', '************', '<EMAIL>', '+13302632266', 'TV segment dedicated to broadcasting senior Independent Study presentations and interviews.');


DROP TABLE IF EXISTS thecollegeofwooster_photos;
CREATE TABLE thecollegeofwooster_photos (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    day INT,
    month INT,
    year INT,
    platform TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_photos (fullname, day, month, year, platform, link) VALUES
('Spring 2025 Campus Blooms Gallery', 2, 4, 2025, 'Instagram', 'https://instagram.com/wooster/photos/spring2025'),
('Winter Gala Highlights', 10, 12, 2024, 'Facebook', 'https://facebook.com/wooster/photos/wintergala2024'),
('Class of 2024 Graduation Moments', 12, 5, 2024, 'Flickr', 'https://flickr.com/photos/wooster/graduation2024'),
('Homecoming 2024 Photo Album', 8, 10, 2024, 'Instagram', 'https://instagram.com/wooster/photos/homecoming2024');


---------------------------
-- Table: thecollegeofwooster_videos
---------------------------
DROP TABLE IF EXISTS thecollegeofwooster_videos;
CREATE TABLE thecollegeofwooster_videos (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    day INTEGER,
    month INTEGER,
    year INTEGER,
    platform TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_videos (fullname, day, month, year, platform, link) VALUES
('Senior Symposium Highlights', 12, 4, 2024, 'YouTube', 'https://youtube.com/wooster/symposium2024'),
('Welcome to Wooster: First-Year Orientation Recap', 23, 8, 2023, 'YouTube', 'https://youtube.com/wooster/orientation2023'),
('Scot Spirit Day Compilation', 7, 9, 2023, 'TikTok', 'https://tiktok.com/@wooster/spiritday'),
('Virtual Campus Tour', 15, 1, 2024, 'YouTube', 'https://youtube.com/wooster/campustour');

------------------------
-------------------------------
-- Table: thecollegeofwooster_accelerators
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_accelerators;
CREATE TABLE thecollegeofwooster_accelerators (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude FLOAT,
    longitude FLOAT
);

INSERT INTO thecollegeofwooster_accelerators (fullname, building, room, hours, phone, email, fax, whatsapp, about, latitude, longitude) VALUES
('Launch Lab Accelerator', 'Morgan Hall', 'Room 204', 'Mon-Fri 9am-5pm', '************', '<EMAIL>', NULL, NULL, 'Supports students developing startup ideas through mentorship and funding access.', 40.8102, -81.9356),
('ScotStart Accelerator', 'Wishart Hall', 'Room 115', 'Mon-Fri 10am-4pm', '************', '<EMAIL>', NULL, NULL, 'Accelerator for student-led social ventures and non-profits.', 40.8087, -81.9374),
('Wooster Tech Boost', 'Taylor Hall', 'Room B10', 'Tue-Thu 11am-5pm', '************', '<EMAIL>', NULL, NULL, 'Technology-focused startup accelerator in partnership with alumni mentors.', 40.8095, -81.9348),
('GreenSpark Innovation Hub', 'Mateer Hall', 'Room 305', 'Mon-Fri 8am-6pm', '************', '<EMAIL>', NULL, NULL, 'Accelerates environmental and sustainability innovations.', 40.8079, -81.9322);

-------------------------------
-- Table: thecollegeofwooster_makerspaces
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_makerspaces;
CREATE TABLE thecollegeofwooster_makerspaces (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    hours TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    about TEXT,
    latitude FLOAT,
    longitude FLOAT
);

INSERT INTO thecollegeofwooster_makerspaces (fullname, building, room, hours, phone, email, fax, whatsapp, about, latitude, longitude) VALUES
('Innovators Forge Makerspace', 'Frick Hall', 'Room 101', 'Mon-Fri 9am-9pm', '************', '<EMAIL>', NULL, NULL, 'Provides tools like 3D printers, CNC routers, and prototyping kits.', 40.8084, -81.9338),
('Creative CoLab', 'Wishart Hall', 'Room 210', 'Mon-Fri 10am-8pm', '************', '<EMAIL>', NULL, NULL, 'A collaborative design and creation space for interdisciplinary projects.', 40.8087, -81.9374),
('Wooster Woodworks', 'Art Building', 'Room 003', 'Sat-Sun 12pm-6pm', '************', '<EMAIL>', NULL, NULL, 'Focuses on woodcraft, furniture design, and hands-on fabrication.', 40.8075, -81.9329),
('BioHack Space', 'Scovel Hall', 'Room B14', 'Mon-Fri 10am-4pm', '************', '<EMAIL>', NULL, NULL, 'A lab for students exploring biotech, bioengineering, and DIY biology.', 40.8091, -81.9341);


-------------------------------
-- Table: thecollegeofwooster_startupfunds
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_startupfunds;
CREATE TABLE thecollegeofwooster_startupfunds (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    phone TEXT,
    email TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_startupfunds (fullname, phone, email, about) VALUES
('Wooster Venture Fund', '************', '<EMAIL>', 'Provides seed funding for student-run ventures and social innovation.'),
('Scot Innovation Fund', '************', '<EMAIL>', 'Competitive fund for Wooster student entrepreneurs pursuing scalable business models.'),
('First-Gen Founders Fund', '************', '<EMAIL>', 'Micro-grants and mentorship for first-generation college entrepreneurs.'),
('Alumni Angels Fund', '************', '<EMAIL>', 'Supported by Wooster alumni investors to fund promising student startups.');


-------------------------------
-- Table: thecollegeofwooster_startups
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_startups;
CREATE TABLE thecollegeofwooster_startups (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    websitelink TEXT
);

INSERT INTO thecollegeofwooster_startups (fullname, about, websitelink) VALUES
('ScotDeliver', 'Student-led startup offering eco-friendly food and product delivery across campus and Wooster area.', 'https://scotdeliver.co'),
('GreenTrack', 'Sustainability app created by Wooster students to monitor and reduce carbon footprints.', 'https://greentrack.app'),
('PeerBridge', 'A peer mentoring platform connecting first-years with upperclassmen for academic support.', 'https://peerbridge.io'),
('Campus Canvas', 'Online student art and design marketplace featuring original Wooster creations.', 'https://campuscanvas.org');


-----------------------------------------------
-------------------------------
-- Table: thecollegeofwooster_researchprojects
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_researchprojects;
CREATE TABLE thecollegeofwooster_researchprojects (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INT,
    researcher TEXT,
    researcher2 TEXT,
    researcher3 TEXT,
    department TEXT,
    facultyorstaffresearch BOOLEAN,
    studentresearch BOOLEAN,
    about TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_researchprojects (fullname, year, researcher, researcher2, researcher3, department, facultyorstaffresearch, studentresearch, about, link) VALUES
('Climate Modeling in the Midwest', 2023, 'Dr. Emily Rhodes', 'James Patel', NULL, 'Environmental Studies', TRUE, TRUE, 'A collaborative research study modeling climate trends and agricultural impact in the Midwest.', 'https://wooster.edu/research/climatemodel'),
('Shakespeare and Social Identity', 2022, 'Prof. Amanda Keller', 'Rachel Kim', 'Marcus Lin', 'English', TRUE, TRUE, 'Explores intersectionality and identity through reinterpretations of Shakespearean plays.', 'https://wooster.edu/research/shakespeare'),
('Neural Networks in Pattern Recognition', 2024, 'Dr. Thomas Givens', NULL, NULL, 'Computer Science', TRUE, FALSE, 'Research on training convolutional neural networks to identify patterns in biometric data.', 'https://wooster.edu/research/neuralnets'),
('Economic Impact of Microloans in Ohio', 2023, 'Lindsey Tran', NULL, NULL, 'Economics', FALSE, TRUE, 'Senior independent study examining microloan programs and economic mobility in rural Ohio.', 'https://wooster.edu/research/microloanstudy');


-------------------------------
-- Table: thecollegeofwooster_theses
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_theses;
CREATE TABLE thecollegeofwooster_theses (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INT,
    author TEXT,
    advisor TEXT,
    advisor2 TEXT,
    department TEXT,
    career TEXT,
    about TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_theses (fullname, year, author, advisor, advisor2, department, career, about, link) VALUES
('The Role of Bees in Pollination Networks', 2023, 'Hannah Roberts', 'Dr. Simon Blake', NULL, 'Biology', 'Environmental Science', 'Analyzes native bee species in northeast Ohio and their role in ecosystem stability.', 'https://wooster.edu/theses/beestudy'),
('Digital Memory and Historical Archives', 2022, 'Noah Fields', 'Prof. Kathryn Walters', 'Dr. Elaine Shaw', 'History', 'Digital Humanities', 'Examines how digital archiving practices shape collective historical memory.', 'https://wooster.edu/theses/digitalarchives'),
('Symbolism in Toni Morrisons Novels', 2024, 'Jordan Ellis', 'Prof. Anita Davis', NULL, 'English', 'Literature', 'A close reading of recurring symbolic themes in Morrisons fiction.', 'https://wooster.edu/theses/morrison'),
('Electrochemical Sensors for Water Quality', 2023, 'Priya Nair', 'Dr. Kevin Lawrence', NULL, 'Chemistry', 'Public Health', 'Develops and tests a portable electrochemical sensor for detecting lead in water.', 'https://wooster.edu/theses/watersensor');


-------------------------------
-- Table: thecollegeofwooster_books
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_books;
CREATE TABLE thecollegeofwooster_books (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INT,
    author TEXT,
    author2 TEXT,
    author3 TEXT,
    facultyorstaffbook BOOLEAN,
    studentbook BOOLEAN,
    publisher TEXT,
    about TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_books (fullname, year, author, author2, author3, facultyorstaffbook, studentbook, publisher, about, link) VALUES
('Race, Space, and Place in American Literature', 2023, 'Dr. Sandra Liu', NULL, NULL, TRUE, FALSE, 'Routledge', 'A literary critique examining spatial politics in American fiction.', 'https://wooster.edu/books/racespace'),
('Advanced Robotics for Beginners', 2024, 'Mark Benson', 'Aliyah Grant', NULL, FALSE, TRUE, 'Campus Press', 'A student-authored robotics primer developed during a senior project.', 'https://wooster.edu/books/robotics101'),
('Postcolonial Ecologies', 2021, 'Prof. Isaac Rodriguez', 'Dr. Mariam Okoro', NULL, TRUE, FALSE, 'Duke University Press', 'A collection of essays on ecological narratives in postcolonial contexts.', 'https://wooster.edu/books/ecologies'),
('The Wooster Writing Guide', 2022, 'Various Students', NULL, NULL, FALSE, TRUE, 'Wooster Writing Center', 'Comprehensive writing manual authored and compiled by Writing Center students.', 'https://wooster.edu/books/writingguide');


-------------------------------
-- Table: thecollegeofwooster_articles
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_articles;
CREATE TABLE thecollegeofwooster_articles (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INT,
    author TEXT,
    author2 TEXT,
    author3 TEXT,
    facultyorstaffarticle BOOLEAN,
    studentarticle BOOLEAN,
    about TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_articles (fullname, year, author, author2, author3, facultyorstaffarticle, studentarticle, about, link) VALUES
('Analyzing Voter Turnout in Midwestern States', 2023, 'Dr. Brian Keller', NULL, NULL, TRUE, FALSE, 'Statistical analysis of voter participation trends in local elections.', 'https://wooster.edu/articles/voterdata'),
('Smart Textiles in Sportswear', 2022, 'Emily Vargas', 'Tyler Cho', NULL, FALSE, TRUE, 'Examines the integration of sensors into wearable athletic gear.', 'https://wooster.edu/articles/smarttextiles'),
('Language Preservation in Appalachian Communities', 2023, 'Prof. Andrea Hill', NULL, NULL, TRUE, FALSE, 'Study of dialects and endangered language practices in rural Appalachia.', 'https://wooster.edu/articles/languagepreservation'),
('Peer-to-Peer Learning in STEM Labs', 2024, 'Maya Desai', 'Jacob Wells', NULL, FALSE, TRUE, 'Discusses informal peer instruction models in undergraduate chemistry labs.', 'https://wooster.edu/articles/peerlearning');


-------------------------------
-- Table: thecollegeofwooster_patents
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_patents;
CREATE TABLE thecollegeofwooster_patents (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INT,
    inventor TEXT,
    inventor2 TEXT,
    inventor3 TEXT,
    facultyorstaffpatent BOOLEAN,
    studentpatent BOOLEAN,
    about TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_patents (fullname, year, inventor, inventor2, inventor3, facultyorstaffpatent, studentpatent, about, link) VALUES
('Portable UV Water Sterilizer', 2022, 'Dr. Benjamin Harris', 'Sara Yeung', NULL, TRUE, TRUE, 'Device for rapid sterilization of drinking water using UV LEDs.', 'https://wooster.edu/patents/uvsterilizer'),
('Eco-friendly Bioplastic Composite', 2023, 'Meghan Ortiz', NULL, NULL, FALSE, TRUE, 'Biodegradable composite made from corn starch and natural resins.', 'https://wooster.edu/patents/bioplastic'),
('Noise-Canceling Classroom Headset', 2024, 'Dr. Olivia Grant', NULL, NULL, TRUE, FALSE, 'Patent for over-ear headset designed for focused learning in noisy environments.', 'https://wooster.edu/patents/headset'),
('Solar-Powered Mobility Aid', 2022, 'Daniel Wu', 'Dr. Amy Lin', NULL, FALSE, TRUE, 'Innovative walker for seniors powered by a compact solar panel.', 'https://wooster.edu/patents/solaraid');

--------------------------------------
-------------------------------
-- Table: thecollegeofwooster_building
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_building;
CREATE TABLE thecollegeofwooster_building (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    location TEXT,
    hours TEXT,
    accessmethod TEXT,
    capacity INT,
    phone TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    mapreferencepoint TEXT,
    about TEXT,
    building BOOLEAN,
    housing BOOLEAN,
    parking BOOLEAN,
    school BOOLEAN,
    residence BOOLEAN,
    library BOOLEAN,
    lab BOOLEAN,
    studio BOOLEAN,
    gallery BOOLEAN,
    theater BOOLEAN,
    coworkingspace BOOLEAN,
    studyspace BOOLEAN,
    fitnessspace BOOLEAN,
    funspace BOOLEAN,
    storagespace BOOLEAN,
    mailingspace BOOLEAN,
    museum BOOLEAN,
    sacredspace BOOLEAN,
    outdoorspace BOOLEAN,
    researchstation BOOLEAN,
    clinic BOOLEAN,
    laundryspace BOOLEAN,
    campusdumpsters BOOLEAN,
    watertanks BOOLEAN,
    other BOOLEAN
);

INSERT INTO thecollegeofwooster_building (
    fullname, location, hours, accessmethod, capacity, phone, latitude, longitude, mapreferencepoint, about,
    building, housing, parking, school, residence, library, lab, studio, gallery, theater,
    coworkingspace, studyspace, fitnessspace, funspace, storagespace, mailingspace, museum,
    sacredspace, outdoorspace, researchstation, clinic, laundryspace, campusdumpsters, watertanks, other
) VALUES
('Kauke Hall', '400 E University St', '8am - 9pm', 'Student ID', 500, '************', 40.8071, -81.9334, 'North Campus', 'Main academic building housing the humanities departments.',
 TRUE, FALSE, FALSE, TRUE, FALSE, FALSE, TRUE, FALSE, FALSE, FALSE, 
 FALSE, TRUE, FALSE, FALSE, TRUE, FALSE, FALSE, 
 FALSE, FALSE, FALSE, FALSE, TRUE, FALSE, FALSE, FALSE),

('Lowry Center', '1189 Beall Ave', '6am - 11pm', 'Student ID', 1000, '************', 40.8089, -81.9331, 'Central Campus', 'Student center with dining, bookstore, and student org offices.',
 TRUE, TRUE, TRUE, FALSE, TRUE, FALSE, FALSE, TRUE, TRUE, TRUE,
 TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, FALSE,
 FALSE, FALSE, FALSE, TRUE, TRUE, TRUE, TRUE, FALSE);

-------------------------------
-- Table: thecollegeofwooster_rooms
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_rooms;
CREATE TABLE thecollegeofwooster_rooms (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    roomtype TEXT,
    dimensions TEXT,
    capacity INT,
    phone TEXT,
    fax TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_rooms (fullname, building, roomtype, dimensions, capacity, phone, fax, about, latitude, longitude) VALUES
('Kauke 305', 'Kauke Hall', 'Classroom', '30x25 ft', 30, '************', '************', 'Smart classroom with projector and whiteboard.', 40.8072, -81.9333),
('Lowry Multipurpose Room', 'Lowry Center', 'Multipurpose', '40x60 ft', 150, '************', NULL, 'Event space for student activities and campus events.', 40.8088, -81.9330);


-------------------------------
-- Table: thecollegeofwooster_roomequipment
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_roomequipment;
CREATE TABLE thecollegeofwooster_roomequipment (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    roomtype TEXT,
    assettag TEXT,
    dateinstalled DATE,
    status TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_roomequipment (fullname, building, room, roomtype, assettag, dateinstalled, status, about) VALUES
('Epson Projector EX9200', 'Kauke Hall', '305', 'Classroom', 'KA305-PRJ01', '2021-08-15', 'Operational', 'High-lumen ceiling-mounted projector with HDMI support.'),
('SmartBoard SB680', 'Lowry Center', 'Multipurpose Room', 'Multipurpose', 'LO-MR-SB01', '2022-01-12', 'Operational', 'Interactive whiteboard used for workshops and meetings.');


-------------------------------
-- Table: thecollegeofwooster_roomassignments
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_roomassignments;
CREATE TABLE thecollegeofwooster_roomassignments (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    gender TEXT
);

INSERT INTO thecollegeofwooster_roomassignments (fullname, building, room, gender) VALUES
('Anna Lee', 'Armington Hall', '210B', 'Female'),
('Derek Chen', 'Kenarden Lodge', '315A', 'Male');


-------------------------------
-- Table: thecollegeofwooster_publicart
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_publicart;
CREATE TABLE thecollegeofwooster_publicart (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    email TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_publicart (fullname, building, room, email, about, latitude, longitude) VALUES
('Solar Spectrum', 'Wishart Hall', 'Lobby', '<EMAIL>', 'An installation of stained glass panels representing diversity in light and learning.', 40.8077, -81.9329),
('Reverence', 'Ebert Art Center', 'Main Gallery', '<EMAIL>', 'Bronze sculpture honoring global perspectives in education.', 40.8068, -81.9325);


-------------------------------
-- Table: thecollegeofwooster_emergencyequipment
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_emergencyequipment;
CREATE TABLE thecollegeofwooster_emergencyequipment (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    about TEXT
);

INSERT INTO thecollegeofwooster_emergencyequipment (fullname, building, room, latitude, longitude, about) VALUES
('AED Unit', 'Lowry Center', 'First Floor Hallway', 40.8089, -81.9331, 'Automated external defibrillator available for cardiac emergencies.'),
('Emergency Eyewash Station', 'Taylor Hall', 'Chem Lab 204', 40.8075, -81.9327, 'Safety station for chemical exposure incidents.');


----------------------------------

-------------------------------
-- Table: thecollegeofwooster_classschedules
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_classschedules;
CREATE TABLE thecollegeofwooster_classschedules (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    starttime TEXT,
    endtime TEXT,
    major TEXT,
    department TEXT,
    _mon BOOLEAN,
    _tue BOOLEAN,
    _wed BOOLEAN,
    _thur BOOLEAN,
    _fri BOOLEAN,
    _sat BOOLEAN,
    _sun BOOLEAN,
    instructor TEXT,
    ta TEXT,
    enrollmentcapacity INT,
    instructionmode TEXT,
    books TEXT,
    startday INT,
    startmonth INT,
    startyear INT,
    endday INT,
    endmonth INT,
    endyear INT,
    location TEXT,
    payment TEXT,
    about TEXT,
    capacity INT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    ticketslink TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_classschedules (
    fullname, building, room, starttime, endtime, major, department, _mon, _tue, _wed, _thur, _fri, _sat, _sun,
    instructor, ta, enrollmentcapacity, instructionmode, books, startday, startmonth, startyear, endday, endmonth, endyear,
    location, payment, about, capacity, phone, email, whatsapp, ticketslink, latitude, longitude
) VALUES
(
    'Introduction to Psychology', 'Morgan Hall', '204', '09:00', '10:15', 'Psychology', 'Psychology', 
    TRUE, FALSE, TRUE, FALSE, TRUE, FALSE, FALSE,
    'Dr. Lisa Tremblay', 'Alex Ramos', 30, 'In-person', 'Psychology 101, Zimbardo', 27, 1, 2025, 5, 5, 2025,
    'Morgan Hall', 'Included in Tuition', 'A foundational course exploring cognitive, behavioral, and developmental psychology.', 30,
    '************', '<EMAIL>', NULL, NULL, 40.8063, -81.9341
);


-------------------------------
-- Table: thecollegeofwooster_weeklyschedule
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_weeklyschedule;
CREATE TABLE thecollegeofwooster_weeklyschedule (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    teamororg TEXT,
    starttime TEXT,
    endtime TEXT,
    _mon BOOLEAN,
    _tue BOOLEAN,
    _wed BOOLEAN,
    _thur BOOLEAN,
    _fri BOOLEAN,
    _sat BOOLEAN,
    _sun BOOLEAN,
    about TEXT,
    startday INT,
    startmonth INT,
    startyear INT,
    endday INT,
    endmonth INT,
    endyear INT,
    location TEXT,
    payment TEXT,
    capacity INT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    ticketslink TEXT,
    major TEXT,
    instructor TEXT,
    ta TEXT,
    enrollmentcapacity INT,
    instructionmode TEXT,
    syllabuscount INT,
    books TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_weeklyschedule (
    fullname, building, room, teamororg, starttime, endtime, _mon, _tue, _wed, _thur, _fri, _sat, _sun,
    about, startday, startmonth, startyear, endday, endmonth, endyear,
    location, payment, capacity, phone, email, whatsapp, ticketslink,
    major, instructor, ta, enrollmentcapacity, instructionmode, syllabuscount, books,
    latitude, longitude
) VALUES
(
    'Scot Band Practice', 'Scheide Music Center', '101', 'Wooster Scot Band', '17:00', '18:30',
    FALSE, TRUE, FALSE, TRUE, FALSE, FALSE, FALSE,
    'Weekly rehearsals for the Scot Marching Band.', 22, 1, 2025, 2, 5, 2025,
    'Scheide Music Center', 'N/A', 80, '************', '<EMAIL>', NULL, NULL,
    NULL, 'Prof. Walter Ames', NULL, 80, 'In-person', 1, NULL, 40.8081, -81.9350
);


-------------------------------
-- Table: thecollegeofwooster_events
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_events;
CREATE TABLE thecollegeofwooster_events (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    location TEXT,
    starttime TEXT,
    endtime TEXT,
    startday INT,
    startmonth INT,
    startyear INT,
    endday INT,
    endmonth INT,
    endyear INT,
    payment TEXT,
    capacity INT,
    phone TEXT,
    email TEXT,
    whatsapp TEXT,
    teamororg TEXT,
    department TEXT,
    admissionskeydate BOOLEAN,
    paymentkeydate BOOLEAN,
    orientationevent BOOLEAN,
    graduationevent BOOLEAN,
    graduationkeydate BOOLEAN,
    alumnievent BOOLEAN,
    communityrentalevent BOOLEAN,
    about TEXT,
    ticketslink TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    _mon BOOLEAN,
    _tue BOOLEAN,
    _wed BOOLEAN,
    _thur BOOLEAN,
    _fri BOOLEAN,
    _sat BOOLEAN,
    _sun BOOLEAN,
    major TEXT,
    instructor TEXT,
    ta TEXT,
    enrollmentcapacity INT,
    instructionmode TEXT,
    books TEXT
);

INSERT INTO thecollegeofwooster_events (
    fullname, building, room, location, starttime, endtime, startday, startmonth, startyear, endday, endmonth, endyear,
    payment, capacity, phone, email, whatsapp, teamororg, department,
    admissionskeydate, paymentkeydate, orientationevent, graduationevent, graduationkeydate, alumnievent, communityrentalevent,
    about, ticketslink, latitude, longitude, _mon, _tue, _wed, _thur, _fri, _sat, _sun,
    major, instructor, ta, enrollmentcapacity, instructionmode, books
) VALUES
(
    'Spring Music Showcase', 'Gault Recital Hall', 'Main Hall', 'Gault Recital Hall',
    '19:00', '21:00', 18, 4, 2025, 18, 4, 2025,
    'Free', 200, '************', '<EMAIL>', NULL, 'Scot Music Society', 'Music',
    FALSE, FALSE, FALSE, FALSE, FALSE, TRUE,
    TRUE, 'Annual spring concert featuring student ensembles.', NULL, 40.8065, -81.9338,
    FALSE, FALSE, FALSE, FALSE, FALSE, TRUE, FALSE,
    NULL, NULL, NULL, NULL, 'In-person', NULL
);

-------------------------------
-- Table: thecollegeofwooster_academiccalendar
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_academiccalendar;
CREATE TABLE thecollegeofwooster_academiccalendar (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    starttime TEXT,
    endtime TEXT,
    startday INT,
    startmonth INT,
    startyear INT,
    endday INT,
    endmonth INT,
    endyear INT
);

INSERT INTO thecollegeofwooster_academiccalendar (
    fullname, starttime, endtime, startday, startmonth, startyear, endday, endmonth, endyear
) VALUES
(
    'Spring Semester 2025', '08:00', '17:00', 13, 1, 2025, 9, 5, 2025
),
(
    'Fall Semester 2025', '08:00', '17:00', 25, 8, 2025, 13, 12, 2025
);



----------------------------
-------------------------------
-- Table: thecollegeofwooster_feedback
-------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_feedback;
CREATE TABLE thecollegeofwooster_feedback (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    link TEXT
);

INSERT INTO thecollegeofwooster_feedback (fullname, link) VALUES
('Course Evaluation Form - Spring 2025', 'https://wooster.edu/forms/feedback/courseeval-spring2025'),
('Campus Dining Feedback', 'https://wooster.edu/forms/feedback/dining'),
('Independent Study Experience Survey', 'https://wooster.edu/forms/feedback/IS-survey'),
('Residence Hall Feedback Form', 'https://wooster.edu/forms/feedback/housing');

---------------------------------------------------

--------------------------------------------
-- Table: thecollegeofwooster_historicaltimeline
--------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_historicaltimeline;
CREATE TABLE thecollegeofwooster_historicaltimeline (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    year INTEGER,
    about TEXT
);

INSERT INTO thecollegeofwooster_historicaltimeline (fullname, year, about) VALUES
('Founding of The College of Wooster', 1866, 'The College of Wooster was founded by the Presbyterian Church in 1866, in Wooster, Ohio.'),
('Opening of Kauke Hall', 1902, 'Kauke Hall, the signature academic building on campus, officially opened in 1902 and remains an iconic structure.'),
('Introduction of Independent Study Program', 1947, 'Wooster introduced its nationally recognized Independent Study program, requiring every student to complete a thesis or project.'),
('Fire at Old Main', 1901, 'A major fire destroyed the original Old Main building. The college quickly rebuilt, leading to the construction of Kauke Hall.');

------------------------------------------
---------------------------------
-- Table: thecollegeofwooster_rentals
---------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_rentals;
CREATE TABLE thecollegeofwooster_rentals (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    dimensions TEXT,
    capacity INTEGER,
    phone TEXT,
    whatsapp TEXT,
    payment TEXT,
    department TEXT,
    facilityrental BOOLEAN,
    equipmentrental BOOLEAN,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    pricing TEXT
);

INSERT INTO thecollegeofwooster_rentals (
    fullname, dimensions, capacity, phone, whatsapp, payment, department, facilityrental, equipmentrental, about, latitude, longitude, pricing
) VALUES
('Gault Recital Hall', '50x30 ft', 200, '************', '+13302632419', 'Fee Required', 'Music Department', TRUE, FALSE, 'Performance space ideal for recitals and lectures.', 40.8070, -81.9335, '$150/hour'),
('Lowry Event Room A', '60x40 ft', 100, '************', '+13302632232', 'Free for Student Orgs', 'Student Life', TRUE, FALSE, 'Multipurpose event space for student organizations.', 40.8078, -81.9341, 'Free or $50/hour for external use'),
('Portable PA System', 'N/A', 0, '************', '+13302632000', 'Deposit Required', 'Campus Events', FALSE, TRUE, 'Portable sound system suitable for small events.', 40.8071, -81.9320, '$20/day'),
('Projector and Screen Package', 'N/A', 0, '************', '+13302632373', 'Deposit Required', 'AV Services', FALSE, TRUE, 'Package includes HD projector, screen, and cables.', 40.8076, -81.9337, '$25/day');


--------------------------------------------------
-- Table: thecollegeofwooster_rentalequipmentcalendar
--------------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_rentalequipmentcalendar;
CREATE TABLE thecollegeofwooster_rentalequipmentcalendar (
    id SERIAL PRIMARY KEY,
    asset TEXT,
    tagoridentifier TEXT,
    department TEXT,
    renter TEXT,
    checkoutday INTEGER,
    checkoutmonth INTEGER,
    checkoutyear INTEGER,
    returnday INTEGER,
    returnmonth INTEGER,
    returnyear INTEGER
);

INSERT INTO thecollegeofwooster_rentalequipmentcalendar (
    asset, tagoridentifier, department, renter, checkoutday, checkoutmonth, checkoutyear, returnday, returnmonth, returnyear
) VALUES
('Portable PA System', 'PA-1021', 'Campus Events', 'John Doe', 10, 4, 2025, 12, 4, 2025),
('Projector and Screen Package', 'PJ-4519', 'AV Services', 'Emily Carter', 9, 4, 2025, 11, 4, 2025),
('GoPro Camera', 'CAM-8890', 'Media Lab', 'Sarah Lee', 8, 4, 2025, 13, 4, 2025),
('Wireless Microphone Set', 'MIC-3372', 'Theatre Department', 'Alex Kim', 11, 4, 2025, 14, 4, 2025);

-----------------------------------------

---------------------------------
-- Table: thecollegeofwooster_jobs
---------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_jobs;
CREATE TABLE thecollegeofwooster_jobs (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    openingday INTEGER,
    openingmonth INTEGER,
    openingyear INTEGER,
    closingday INTEGER,
    closingmonth INTEGER,
    closingyear INTEGER,
    email TEXT,
    link TEXT,
    adminjob BOOLEAN,
    academicjob BOOLEAN,
    about TEXT
);

INSERT INTO thecollegeofwooster_jobs (
    fullname, openingday, openingmonth, openingyear, closingday, closingmonth, closingyear, email, link, adminjob, academicjob, about
) VALUES
('Assistant Professor of Chemistry', 1, 4, 2025, 30, 4, 2025, '<EMAIL>', 'https://wooster.edu/jobs/assistant-professor-chemistry', FALSE, TRUE, 'Tenure-track position focused on organic chemistry and undergraduate research mentoring.'),
('Admissions Counselor', 5, 4, 2025, 20, 4, 2025, '<EMAIL>', 'https://wooster.edu/jobs/admissions-counselor', TRUE, FALSE, 'Coordinate prospective student outreach, manage application reviews, and represent Wooster at college fairs.'),
('IT Help Desk Technician', 10, 4, 2025, 24, 4, 2025, '<EMAIL>', 'https://wooster.edu/jobs/it-helpdesk', TRUE, FALSE, 'Provides front-line technical support to students and staff with computing, printing, and AV needs.'),
('Visiting Lecturer in English', 3, 4, 2025, 25, 4, 2025, '<EMAIL>', 'https://wooster.edu/jobs/visiting-lecturer-english', FALSE, TRUE, 'One-year appointment in literature and composition, with opportunities to guide Independent Study.');


--------------------------------------------
------------------------------------
-- Table: thecollegeofwooster_services
------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_services;
CREATE TABLE thecollegeofwooster_services (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    price TEXT,
    requirements TEXT,
    time TEXT,
    department TEXT,
    link TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_services (
    fullname, price, requirements, time, department, link, about
) VALUES
('Printing Services', 'Varies by size', 'Student ID', 'Available 9am-5pm', 'Campus Services', 'https://wooster.edu/services/printing', 'Print documents, posters, and flyers on campus. Prices vary based on the material size and color.'),
('Counseling Services', 'Free', 'Student enrollment', 'By appointment', 'Health and Wellness', 'https://wooster.edu/services/counseling', 'Confidential mental health counseling available for students. Includes individual, group, and emergency counseling.'),
('Library Research Assistance', 'Free', 'Library Access', 'Available 8am-9pm', 'Library Services', 'https://wooster.edu/services/library-research', 'Research support for students working on Independent Study, class projects, or general inquiries.'),
('Fitness Center Membership', '$50/semester', 'Valid Student ID', 'Open 6am-10pm', 'Recreation Services', 'https://wooster.edu/services/fitness-center', 'Access to the on-campus fitness center, which includes cardio equipment, weights, and group exercise classes.');
-----------------------------------------------------------------
---------------------------------------
-- Table: thecollegeofwooster_atms
---------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_atms;
CREATE TABLE thecollegeofwooster_atms (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    building TEXT,
    room TEXT,
    about TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_atms (
    fullname, building, room, about, latitude, longitude
) VALUES
('ATM - Lowry Center', 'Lowry Center', 'Near the main entrance', '24/7 access to ATMs for student convenience and cash withdrawals.', 40.8075, -81.9342),
('ATM - Timken Science Library', 'Timken Science Library', 'Near the back entrance', 'ATM available for easy access for library visitors.', 40.8079, -81.9331),
('ATM - C-Store', 'C-Store', 'Near the student center', 'Located at the C-Store, for quick cash withdrawals while shopping.', 40.8082, -81.9338),
('ATM - Wooster Campus Center', 'Wooster Campus Center', 'Near the lounge', 'ATM provides students with access to cash anytime the campus center is open.', 40.8085, -81.9345);


---------------------------------------
-- Table: thecollegeofwooster_payments
---------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_payments;
CREATE TABLE thecollegeofwooster_payments (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    link TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_payments (
    fullname, link, about
) VALUES
('Tuition Payment Portal', 'https://wooster.edu/pay/tuition', 'The portal where students can pay their tuition fees securely online. Payment can be done by credit card or bank transfer.'),
('Student Activity Fee Payment', 'https://wooster.edu/pay/activities', 'Students can pay the mandatory student activity fee to support campus events and activities.'),
('Room and Board Payment', 'https://wooster.edu/pay/roomboard', 'Payment for housing and meal plans for students living on campus. Available through the student portal.'),
('Campus Bookstore Payment', 'https://wooster.edu/pay/bookstore', 'Pay for textbooks, campus merchandise, and other academic materials directly through the bookstore payment system.');

-----------------------------------------

-----------------------------------------
-- Table: thecollegeofwooster_clinicsorhospitals
-----------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_clinicsorhospitals;
CREATE TABLE thecollegeofwooster_clinicsorhospitals (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT,
    phone TEXT,
    email TEXT,
    fax TEXT,
    whatsapp TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION,
    daysnhours TEXT,
    building TEXT,
    room TEXT,
    address TEXT,
    postaladdress TEXT,
    payment TEXT,
    mission TEXT,
    vision TEXT,
    corevalues TEXT,
    acceptableclients TEXT,
    acceptablehealthinsurance TEXT,
    admissions TEXT,
    visitors TEXT,
    visitinghours TEXT,
    discharge TEXT,
    parking TEXT
);

INSERT INTO thecollegeofwooster_clinicsorhospitals (
    fullname, about, phone, email, fax, whatsapp, latitude, longitude, daysnhours, building, room, address, postaladdress, payment, mission, vision, corevalues, acceptableclients, acceptablehealthinsurance, admissions, visitors, visitinghours, discharge, parking
) VALUES
('Wooster Health Services', 'On-campus medical facility providing healthcare services for students.', '************', '<EMAIL>', '************', '************', 40.8081, -81.9333, 'Mon-Fri 9am-5pm', 'Lowry Center', 'Room 101', '1189 Beall Ave, Wooster, OH 44691', '1189 Beall Ave, Wooster, OH 44691', 'Free for students', 'Provide accessible healthcare and health education to students.', 'A healthy campus community supported by professional and compassionate care.', 'Integrity, Respect, Excellence', 'All Wooster students', 'Most health insurances accepted', 'Walk-in or appointment required', 'Visitors allowed during visiting hours', '9am-4pm', 'Discharge upon stabilization or referral to a specialist', 'Free parking available near the clinic.');



-------------------------------------------
-- Table: thecollegeofwooster_counselingservices
-------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_counselingservices;
CREATE TABLE thecollegeofwooster_counselingservices (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    price TEXT,
    requirements TEXT,
    time TEXT,
    department TEXT,
    link TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_counselingservices (
    fullname, price, requirements, time, department, link, about
) VALUES
('Counseling and Psychological Services', 'Free for students', 'Student ID required', 'By appointment', 'Health and Wellness', 'https://wooster.edu/services/counseling', 'Confidential counseling services for mental health, stress management, and other concerns. Includes individual, couples, and group counseling.');



------------------------------
-----------------------------------------
-- Table: thecollegeofwooster_emergencycontacts
-----------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_emergencycontacts;
CREATE TABLE thecollegeofwooster_emergencycontacts (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    title TEXT,
    department TEXT,
    school TEXT,
    school2 TEXT,
    school3 TEXT,
    building TEXT,
    room TEXT,
    officehours TEXT,
    phone TEXT,
    ext TEXT,
    email TEXT,
    fax TEXT,
    hours TEXT,
    about TEXT,
    services TEXT,
    latitude DOUBLE PRECISION,
    longitude DOUBLE PRECISION
);

INSERT INTO thecollegeofwooster_emergencycontacts (
    fullname, title, department, school, school2, school3, building, room, officehours, phone, ext, email, fax, hours, about, services, latitude, longitude
) VALUES
('Campus Safety Office', 'Director', 'Campus Safety', 'Campus Services', NULL, NULL, 'Wooster Campus Center', 'Room 201', 'Mon-Fri 8am-5pm', '************', '1234', '<EMAIL>', '************', '24/7 emergency response', 'Provides emergency response services and safety information on campus.', 'Emergency assistance, safety patrols, incident reporting', 40.8087, -81.9340),
('Student Health Services', 'Director', 'Health Services', 'Student Affairs', NULL, NULL, 'Lowry Center', 'Room 101', 'Mon-Fri 9am-5pm', '************', '5678', '<EMAIL>', '************', 'Walk-in or appointment', 'Provides health care and emergency medical services to students.', 'Medical care, health advice, emergency health services', 40.8081, -81.9333),
('Counseling Services', 'Director', 'Counseling', 'Health and Wellness', NULL, NULL, 'Wooster Campus Center', 'Room 202', 'Mon-Fri 9am-5pm', '************', '1235', '<EMAIL>', '************', 'Confidential counseling and mental health services', 'Provides counseling and mental health support for students.', 'Individual and group counseling, mental health support', 40.8084, -81.9337),
('Public Safety Officer', 'Officer', 'Public Safety', 'Campus Safety', NULL, NULL, 'Public Safety Building', 'Room 100', '24/7', '************', '0001', '<EMAIL>', '************', 'Available 24/7 for emergencies', 'Campus-wide safety and emergency response services.', 'Emergency response, safety escorts, accident investigations', 40.8078, -81.9339);


-------------------------------------------
-- Table: thecollegeofwooster_safetyprocedures
-------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_safetyprocedures;
CREATE TABLE thecollegeofwooster_safetyprocedures (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_safetyprocedures (
    fullname, about
) VALUES
('Fire Safety Protocol', 'In case of a fire, evacuate the building immediately and follow emergency exit routes. Fire alarms and exit signs are clearly marked. Do not use elevators during evacuation. Report to the designated assembly area and await further instructions.'),
('Active Shooter Response', 'In case of an active shooter situation, run if possible, hide if escape is not an option, and as a last resort, fight to defend yourself. Call 911 and follow emergency responder instructions.'),
('Weather Emergency Procedures', 'During severe weather conditions, such as tornadoes or thunderstorms, take cover in designated shelter areas on campus. Stay informed through emergency alerts and follow any evacuation instructions.'),
('Medical Emergency Procedures', 'In the event of a medical emergency, call 911 immediately. Notify Campus Safety or Health Services if further assistance is needed. For non-life-threatening emergencies, contact the health center directly.');


----------------------------------------
-------------------------------------------
-- Table: thecollegeofwooster_connectivity
-------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_connectivity;
CREATE TABLE thecollegeofwooster_connectivity (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_connectivity (
    fullname, about
) VALUES
('Campus Wi-Fi', 'The College of Wooster provides high-speed Wi-Fi throughout campus, including all academic buildings, residence halls, and outdoor spaces. Access is available to all students, faculty, and staff using college credentials.'),
('VPN Access', 'The college offers Virtual Private Network (VPN) services to students, faculty, and staff for secure access to campus resources remotely. VPN is required for accessing certain internal networks and databases outside campus.'),
('Printing Network', 'The College of Wooster has a centralized printing network for students, faculty, and staff. Print stations are available in various locations across campus, and students can access print quotas through their accounts.'),
('Tech Support Services', 'The college provides tech support for all campus connectivity issues, including Wi-Fi access, VPN setup, and device troubleshooting. The helpdesk can assist with network-related problems and other technology concerns on campus.');

-----------------------------------------
-------------------------------------------
-- Table: thecollegeofwooster_giving
-------------------------------------------
DROP TABLE IF EXISTS thecollegeofwooster_giving;
CREATE TABLE thecollegeofwooster_giving (
    id SERIAL PRIMARY KEY,
    fullname TEXT,
    about TEXT
);

INSERT INTO thecollegeofwooster_giving (
    fullname, about
) VALUES
('Annual Fund', 'The Annual Fund is a key part of The College of Wooster’s fundraising efforts, supporting scholarships, faculty development, campus improvements, and student activities. Every gift to the Annual Fund makes an immediate impact on students and enhances the academic experience.'),
('Endowment Fund', 'The Endowment Fund supports long-term financial stability at The College of Wooster. Gifts to the endowment help ensure that future generations of students will benefit from the college’s commitment to excellence in education, research, and community engagement.'),
('Scholarship Fund', 'The Scholarship Fund provides financial assistance to students who demonstrate academic achievement, leadership, and community involvement. Contributions to the fund help make a Wooster education accessible to deserving students regardless of their financial situation.'),
('Planned Giving', 'Planned Giving allows alumni and friends of the college to make a lasting impact on Wooster’s future by designating gifts through bequests, charitable gift annuities, or other estate planning instruments. These gifts help secure the college’s mission for years to come.');

















in the path page, lets implement features for the admissions page. when we click admissions we should be taken to admissions page where we see admissionadmissionprocess,registrationprocess, selection,faqs,keydates,links,documents grid items. admissionprocess:    id SERIAL PRIMARY KEY,
    stepnumber INT,
    stepname TEXT,
    description TEXT,
    link TEXT,
    department TEXT,
    targetaudience TEXT,
    estimatedtime TEXT,
    requiredmaterials TEXT. registrationprocess:    id SERIAL PRIMARY KEY,
    stepnumber INT,
    stepname TEXT,
    description TEXT,
    link TEXT,
    department TEXT,
    targetaudience TEXT,
    term TEXT,
    deadline DATE. selection:    id SERIAL PRIMARY KEY,
    firstname TEXT,
    lastname TEXT,
    gender TEXT,
    year INTEGER,
    major TEXT,
    nationality TEXT,
    yearofentry INTEGER. for faqs just pull from faq table where admissionsquestion is true or yes. for links pull from links table where admissionslink is true or yes. for keydates pull from events table where admissionskeydate is true or yes. for documents pull from documents table where admissionsdocument is true or yes